<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mermaid 增强功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .mermaid-container {
            margin: 2rem 0;
            padding: 1rem;
            background: #f9f9f9;
            border: 1px solid #e1e4e8;
            border-radius: 8px;
            overflow-x: auto;
        }
        
        .mermaid-diagram {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100px;
        }
        
        .mermaid-diagram svg {
            max-width: 100% !important;
            height: auto !important;
        }
        
        .test-section {
            margin: 40px 0;
            padding: 20px;
            border: 2px solid #e1e4e8;
            border-radius: 8px;
        }
        
        h2 {
            color: #24292e;
            border-bottom: 1px solid #e1e4e8;
            padding-bottom: 10px;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <h1>🧪 Mermaid 功能测试页面</h1>
    
    <div class="info status">
        <strong>测试说明：</strong>这个页面用于测试 Claudia 主题的 Mermaid 集成功能。如果下面的图表正确显示，说明集成成功。
    </div>
    
    <div class="test-section">
        <h2>测试 1: 基本流程图</h2>
        <p>测试基本的流程图渲染功能：</p>
        
        <pre><code class="language-mermaid">
graph TD
    A[开始] --> B{检查 Mermaid}
    B -->|已启用| C[加载库文件]
    B -->|未启用| D[跳过渲染]
    C --> E[检测代码块]
    E --> F[渲染图表]
    F --> G[显示成功]
    D --> H[结束]
    G --> H
        </code></pre>
    </div>
    
    <div class="test-section">
        <h2>测试 2: 序列图</h2>
        <p>测试序列图的渲染：</p>
        
        <pre><code class="language-mermaid">
sequenceDiagram
    participant U as 用户
    participant T as 主题
    participant M as Mermaid.js
    
    U->>T: 访问包含图表的页面
    T->>T: 检查配置是否启用
    T->>M: 加载 Mermaid 库
    M-->>T: 库加载完成
    T->>M: 初始化配置
    T->>M: 渲染图表
    M-->>U: 显示渲染后的图表
        </code></pre>
    </div>
    
    <div class="test-section">
        <h2>测试 3: 简单类图</h2>
        <p>测试类图的渲染：</p>
        
        <pre><code class="language-mermaid">
classDiagram
    class MermaidIntegration {
        +boolean enabled
        +string theme
        +init()
        +render()
    }
    
    class ClaudiaTheme {
        +loadScript()
        +detectDiagrams()
    }
    
    ClaudiaTheme --> MermaidIntegration : uses
        </code></pre>
    </div>
    
    <div class="test-section">
        <h2>测试 4: 状态图</h2>
        <p>测试状态图的渲染：</p>
        
        <pre><code class="language-mermaid">
stateDiagram-v2
    [*] --> 初始化
    初始化 --> 配置检查
    配置检查 --> 启用: mermaid.enable = true
    配置检查 --> 禁用: mermaid.enable = false
    启用 --> 加载库
    加载库 --> 渲染图表
    渲染图表 --> 完成
    禁用 --> 跳过
    跳过 --> 完成
    完成 --> [*]
        </code></pre>
    </div>
    
    <div id="test-results" class="test-section">
        <h2>🔍 自动测试结果</h2>
        <div id="config-test" class="info status">正在检查配置...</div>
        <div id="library-test" class="info status">正在检查库加载...</div>
        <div id="render-test" class="info status">正在检查渲染功能...</div>
    </div>

    <script>
        // 模拟主题配置
        window.CLAUDIA_CONFIG = {
            mermaid: {
                enable: true,
                theme: 'default'
            }
        };
    </script>
    
    <script src="source/js/mermaid.js"></script>
    
    <script>
        // 测试脚本
        setTimeout(() => {
            const configTest = document.getElementById('config-test');
            const libraryTest = document.getElementById('library-test');
            const renderTest = document.getElementById('render-test');
            
            // 测试配置
            if (window.CLAUDIA_CONFIG && window.CLAUDIA_CONFIG.mermaid && window.CLAUDIA_CONFIG.mermaid.enable) {
                configTest.className = 'success status';
                configTest.innerHTML = '✅ 配置检查通过：Mermaid 已启用';
            } else {
                configTest.className = 'error status';
                configTest.innerHTML = '❌ 配置检查失败：Mermaid 未正确配置';
            }
            
            // 测试库加载
            setTimeout(() => {
                if (typeof mermaid !== 'undefined') {
                    libraryTest.className = 'success status';
                    libraryTest.innerHTML = '✅ 库加载检查通过：Mermaid.js 已成功加载';
                    
                    // 测试渲染
                    setTimeout(() => {
                        const diagrams = document.querySelectorAll('.mermaid-diagram');
                        if (diagrams.length > 0) {
                            renderTest.className = 'success status';
                            renderTest.innerHTML = `✅ 渲染检查通过：发现 ${diagrams.length} 个已渲染的图表`;
                        } else {
                            renderTest.className = 'error status';
                            renderTest.innerHTML = '❌ 渲染检查失败：未发现已渲染的图表';
                        }
                    }, 2000);
                } else {
                    libraryTest.className = 'error status';
                    libraryTest.innerHTML = '❌ 库加载检查失败：Mermaid.js 未成功加载';
                    renderTest.className = 'error status';
                    renderTest.innerHTML = '❌ 渲染检查跳过：依赖库未加载';
                }
            }, 1000);
        }, 500);
    </script>
</body>
</html>
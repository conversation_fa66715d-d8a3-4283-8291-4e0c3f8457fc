<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mermaid 增强功能演示</title>
    <link rel="stylesheet" href="source/style/mermaid.scss">
    <style>
        :root {
            --bg-color: #fff;
            --text-color: #333;
            --border-color: #e1e4e8;
            --primary-color: #409eff;
            --hover-bg-color: #f6f8fa;
            --header-bg-color: #f8f9fa;
            --post-bg-color: #fff;
            --error-color: #f56565;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: #f8f9fa;
            color: var(--text-color);
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 2rem;
            padding: 2rem;
            background: var(--bg-color);
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .demo-header h1 {
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .feature-card {
            background: var(--bg-color);
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .feature-card h3 {
            color: var(--primary-color);
            margin-top: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .feature-icon {
            font-size: 1.2em;
        }
        
        .demo-section {
            margin: 3rem 0;
            padding: 2rem;
            background: var(--bg-color);
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .demo-section h2 {
            color: var(--text-color);
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 0.5rem;
            margin-bottom: 1.5rem;
        }
        
        .instructions {
            background: #e3f2fd;
            border-left: 4px solid var(--primary-color);
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 4px;
        }
        
        .instructions strong {
            color: var(--primary-color);
        }
        
        /* Include basic Mermaid styles */
        .mermaid-container {
            position: relative;
            margin: 2rem 0;
            padding: 1rem;
            background: var(--post-bg-color);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            overflow-x: auto;
        }

        .mermaid-container:hover .mermaid-actions,
        .mermaid-container:focus-within .mermaid-actions {
            opacity: 1;
            transform: translateY(0);
        }

        .mermaid-diagram {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100px;
        }

        .mermaid-diagram svg {
            max-width: 100% !important;
            height: auto !important;
        }

        /* Inline controls styles */
        .mermaid-actions {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            display: flex;
            gap: 0.25rem;
            opacity: 0;
            transform: translateY(-4px);
            transition: all 0.2s ease;
            z-index: 10;
        }

        @media (max-width: 768px) {
            .mermaid-actions {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .mermaid-action {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            background: var(--bg-color);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-color);
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.15s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .mermaid-action:hover {
            background: var(--hover-bg-color);
            border-color: var(--primary-color);
            color: var(--primary-color);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        /* Dark mode toggle for demo */
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            z-index: 1000;
        }
        
        /* Dark theme */
        [data-theme="dark"] {
            --bg-color: #1a202c;
            --text-color: #e2e8f0;
            --border-color: #4a5568;
            --hover-bg-color: #2d3748;
            --header-bg-color: #2d3748;
            --post-bg-color: #1a202c;
        }
        
        [data-theme="dark"] body {
            background: #0f1419;
        }
        
        [data-theme="dark"] .instructions {
            background: #1e3a5f;
            border-color: #63b3ed;
        }
    </style>
</head>
<body>
    <button class="theme-toggle" onclick="toggleTheme()">🌙 切换主题</button>
    
    <div class="demo-header">
        <h1>🎯 Mermaid 增强功能演示</h1>
        <p>体验全新的交互式 Mermaid 图表功能</p>
    </div>

    <div class="features-grid">
        <div class="feature-card">
            <h3><span class="feature-icon">🔍</span> 内联缩放</h3>
            <p>悬停图表时显示缩放控件，支持放大和缩小操作。</p>
        </div>
        <div class="feature-card">
            <h3><span class="feature-icon">🖼️</span> 全屏查看</h3>
            <p>点击全屏按钮在专用模态框中查看大型图表。</p>
        </div>
        <div class="feature-card">
            <h3><span class="feature-icon">⚙️</span> 智能控件</h3>
            <p>适应窗口、重置缩放、精确缩放等高级控制功能。</p>
        </div>
        <div class="feature-card">
            <h3><span class="feature-icon">⌨️</span> 键盘导航</h3>
            <p>支持 ESC 键关闭、点击背景关闭等便捷操作。</p>
        </div>
    </div>

    <div class="demo-section">
        <h2>🔄 复杂流程图</h2>
        <div class="instructions">
            <strong>操作指南：</strong>悬停图表查看控件 → 使用 +/- 缩放 → 点击 ⤢ 全屏查看
        </div>
        
        <div class="mermaid-container">
            <div class="mermaid-diagram">
graph TB
    A[用户访问页面] --> B{检测Mermaid代码}
    B -->|发现代码块| C[加载Mermaid.js]
    B -->|无代码块| D[跳过处理]
    C --> E[初始化配置]
    E --> F[渲染图表]
    F --> G[添加交互控件]
    G --> H{用户操作}
    H -->|缩放| I[调整SVG变换]
    H -->|全屏| J[打开模态框]
    H -->|主题切换| K[重新渲染]
    I --> L[更新显示]
    J --> M[模态框控件]
    M --> N{模态框操作}
    N -->|适应窗口| O[计算最佳缩放]
    N -->|重置| P[恢复100%]
    N -->|缩放| Q[精确调整]
    N -->|关闭| R[关闭模态框]
    K --> F
    L --> H
    O --> H
    P --> H
    Q --> H
    R --> H
    D --> S[页面加载完成]
            </div>
        </div>
    </div>

    <div class="demo-section">
        <h2>📊 序列图示例</h2>
        <div class="instructions">
            <strong>提示：</strong>这个序列图展示了用户与增强功能的交互流程
        </div>
        
        <div class="mermaid-container">
            <div class="mermaid-diagram">
sequenceDiagram
    participant U as 用户
    participant P as 页面
    participant M as Mermaid引擎
    participant C as 控件系统
    participant Modal as 模态框

    U->>P: 访问包含图表的页面
    P->>M: 检测并加载Mermaid
    M->>M: 渲染图表
    M->>C: 附加交互控件
    C->>P: 显示悬浮控件
    
    Note over U,C: 内联交互
    U->>C: 悬停图表
    C->>P: 显示控件 (+, -, ⤢)
    U->>C: 点击缩放按钮
    C->>P: 调整图表缩放
    
    Note over U,Modal: 全屏交互
    U->>C: 点击全屏按钮
    C->>Modal: 打开模态框
    Modal->>M: 重新渲染图表
    Modal->>U: 显示增强控件
    U->>Modal: 使用模态框控件
    Modal->>Modal: 执行操作
    U->>Modal: 按ESC或点击关闭
    Modal->>P: 关闭模态框
            </div>
        </div>
    </div>

    <div class="demo-section">
        <h2>🏗️ 类图示例</h2>
        <div class="instructions">
            <strong>测试功能：</strong>尝试在模态框中使用"适应窗口"功能查看完整类图
        </div>
        
        <div class="mermaid-container">
            <div class="mermaid-diagram">
classDiagram
    class MermaidRenderer {
        +config: Object
        +uiState: UIState
        +init()
        +loadLibrary()
        +renderDiagrams()
        +attachControls()
        +handleThemeChange()
    }
    
    class UIState {
        +modalEl: HTMLElement
        +modalScale: number
        +modalMinScale: number
        +modalMaxScale: number
        +modalScaleStep: number
    }
    
    class InlineControls {
        +container: HTMLElement
        +scale: number
        +addControls()
        +applyScale()
        +handleZoomIn()
        +handleZoomOut()
        +openFullscreen()
    }
    
    class ModalSystem {
        +modal: HTMLElement
        +content: HTMLElement
        +toolbar: HTMLElement
        +createModal()
        +openModal()
        +closeModal()
        +fitToWindow()
        +applyScale()
        +handleKeyboard()
    }
    
    class ThemeManager {
        +currentTheme: string
        +detectTheme()
        +switchTheme()
        +updateConfig()
        +reRenderDiagrams()
    }
    
    MermaidRenderer --> UIState
    MermaidRenderer --> InlineControls
    MermaidRenderer --> ModalSystem
    MermaidRenderer --> ThemeManager
    InlineControls --> ModalSystem
    ThemeManager --> MermaidRenderer
            </div>
        </div>
    </div>

    <div class="demo-section">
        <h2>📈 甘特图示例</h2>
        <div class="instructions">
            <strong>性能提示：</strong>大型图表会自动在模态框中适应窗口大小
        </div>
        
        <div class="mermaid-container">
            <div class="mermaid-diagram">
gantt
    title Mermaid增强功能开发计划
    dateFormat  YYYY-MM-DD
    section 研究阶段
    需求分析           :done,    des1, 2024-01-01,2024-01-03
    技术调研           :done,    des2, 2024-01-02, 2024-01-05
    架构设计           :done,    des3, 2024-01-04, 2024-01-07
    
    section 开发阶段
    基础功能实现        :done,    dev1, 2024-01-06, 2024-01-12
    内联控件开发        :done,    dev2, 2024-01-10, 2024-01-15
    模态框系统          :done,    dev3, 2024-01-13, 2024-01-18
    样式集成           :done,    dev4, 2024-01-16, 2024-01-20
    
    section 测试阶段
    功能测试           :active,  test1, 2024-01-18, 2024-01-22
    兼容性测试          :        test2, 2024-01-20, 2024-01-24
    性能优化           :        test3, 2024-01-22, 2024-01-26
    
    section 发布阶段
    文档编写           :        doc1, 2024-01-24, 2024-01-28
    版本发布           :        rel1, 2024-01-26, 2024-01-30
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <script>
        // 模拟主题配置
        window.CLAUDIA_CONFIG = {
            mermaid: {
                enable: true,
                theme: 'default'
            }
        };
    </script>
    <script src="source/js/mermaid.js"></script>
    <script>
        // 主题切换功能
        function toggleTheme() {
            const html = document.documentElement;
            const currentTheme = html.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            html.setAttribute('data-theme', newTheme);
            
            // 触发主题变更事件
            window.dispatchEvent(new Event('theme-changed'));
            
            // 更新按钮文本
            const button = document.querySelector('.theme-toggle');
            button.textContent = newTheme === 'dark' ? '☀️ 切换主题' : '🌙 切换主题';
        }
        
        // 添加一些演示说明
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎯 Mermaid 增强功能演示已加载');
            console.log('💡 提示：');
            console.log('  - 悬停图表查看内联控件');
            console.log('  - 点击全屏按钮体验模态框');
            console.log('  - 使用ESC键或点击背景关闭模态框');
            console.log('  - 尝试切换主题查看适配效果');
        });
    </script>
</body>
</html>

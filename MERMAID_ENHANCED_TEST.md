# Mermaid 增强功能测试

这是一个测试文件，用于验证 Mermaid 图表的增强交互功能，包括内联缩放、全屏模态框和高级控件。

## 🎯 功能特性

### ✨ 内联交互控件
- 🔍 **缩放控件**：鼠标悬停时显示放大/缩小/重置按钮
- 🖼️ **全屏按钮**：点击打开专用查看模态框
- 🖱️ **鼠标滚轮缩放**：在图表上滚动鼠标滚轮进行缩放
- 📱 **触摸缩放**：移动端双指缩放支持

### 🖼️ 全屏模态框功能
- 🎛️ **完整控件栏**：放大、缩小、重置、适应窗口、关闭
- 🖱️ **拖拽移动**：在模态框中拖拽图表查看不同部分
- ⌨️ **键盘快捷键**：ESC关闭、Ctrl+/- 缩放、Ctrl+0 重置
- 🎯 **智能适应**：自动调整图表大小以适应模态框

## 📊 测试图表

### 复杂流程图测试 - 适合测试缩放功能

```mermaid
graph TD
    A[用户访问页面] --> B{检查Mermaid配置}
    B -->|已启用| C[加载Mermaid.js库]
    B -->|未启用| D[跳过渲染]
    
    C --> E[扫描页面内容]
    E --> F{发现Mermaid代码块?}
    F -->|是| G[解析代码块内容]
    F -->|否| H[完成页面加载]
    
    G --> I[创建SVG容器]
    I --> J[渲染Mermaid图表]
    J --> K[添加交互控件]
    K --> L[绑定事件监听器]
    
    L --> M{用户交互}
    M -->|鼠标悬停| N[显示控件栏]
    M -->|滚轮缩放| O[执行内联缩放]
    M -->|点击全屏| P[打开模态框]
    M -->|触摸缩放| Q[处理手势缩放]
    
    N --> R[等待用户操作]
    O --> S[更新变换矩阵]
    P --> T[初始化模态框]
    Q --> S
    
    T --> U[克隆SVG元素]
    U --> V[设置模态框控件]
    V --> W[启用拖拽功能]
    W --> X{模态框交互}
    
    X -->|拖拽| Y[更新图表位置]
    X -->|缩放控件| Z[调整缩放级别]
    X -->|键盘快捷键| AA[执行对应操作]
    X -->|点击背景/ESC| BB[关闭模态框]
    
    S --> CC[应用CSS变换]
    Y --> CC
    Z --> CC
    AA --> CC
    
    CC --> DD[更新显示效果]
    BB --> EE[清理模态框资源]
    DD --> FF[等待下次交互]
    EE --> FF
    
    D --> H
    H --> GG[页面加载完成]
    FF --> GG
    
    style A fill:#e1f5fe
    style B fill:#fff3e0
    style C fill:#f3e5f5
    style P fill:#e8f5e8
    style T fill:#fff8e1
```

### 复杂序列图测试 - 适合测试全屏查看

```mermaid
sequenceDiagram
    participant U as 用户
    participant B as 浏览器
    participant P as 页面渲染器
    participant M as Mermaid引擎
    participant C as 控件管理器
    participant Modal as 模态框系统
    
    Note over U,Modal: 页面初始化阶段
    U->>B: 访问包含Mermaid的页面
    B->>P: 开始页面解析
    P->>M: 检测到mermaid代码块
    M->>M: 加载Mermaid.js库
    M->>P: 渲染SVG图表
    P->>C: 添加交互控件
    C->>B: 绑定事件监听器
    B-->>U: 显示带控件的图表
    
    Note over U,Modal: 内联交互阶段
    U->>B: 鼠标悬停在图表上
    B->>C: 触发hover事件
    C-->>U: 显示控件栏
    
    alt 滚轮缩放
        U->>B: 滚动鼠标滚轮
        B->>C: wheel事件
        C->>C: 计算缩放比例
        C->>B: 应用CSS变换
        B-->>U: 更新图表显示
    else 点击缩放按钮
        U->>C: 点击放大/缩小按钮
        C->>C: 调整缩放状态
        C->>B: 应用新的变换
        B-->>U: 显示缩放后的图表
    end
    
    Note over U,Modal: 全屏模态框阶段
    U->>C: 点击全屏按钮
    C->>Modal: 创建模态框实例
    Modal->>Modal: 克隆SVG元素
    Modal->>Modal: 设置模态框UI
    Modal->>B: 显示模态框
    B->>B: 禁用body滚动
    B-->>U: 显示全屏图表查看器
    
    Note over U,Modal: 模态框交互阶段
    loop 用户在模态框中的操作
        alt 拖拽图表
            U->>Modal: 鼠标拖拽
            Modal->>Modal: 计算移动距离
            Modal->>B: 更新图表位置
        else 使用缩放控件
            U->>Modal: 点击缩放按钮
            Modal->>Modal: 调整缩放级别
            Modal->>B: 应用变换
        else 键盘快捷键
            U->>B: 按下Ctrl+/- 或 ESC
            B->>Modal: 键盘事件
            Modal->>Modal: 执行对应操作
        else 适应窗口
            U->>Modal: 点击适应窗口按钮
            Modal->>Modal: 计算最佳缩放比例
            Modal->>B: 应用自动缩放
        end
        B-->>U: 更新图表显示
    end
    
    Note over U,Modal: 关闭模态框
    alt 点击关闭按钮
        U->>Modal: 点击X按钮
    else 按ESC键
        U->>B: 按ESC键
        B->>Modal: keydown事件
    else 点击背景
        U->>Modal: 点击模态框背景
    end
    
    Modal->>Modal: 清理模态框资源
    Modal->>B: 移除模态框DOM
    B->>B: 恢复body滚动
    B-->>U: 返回正常页面视图
    
    Note over U,Modal: 移动端支持
    Note right of U: 📱 触摸设备特殊处理
    U->>B: 双指缩放手势
    B->>C: touchstart/touchmove事件
    C->>C: 计算手指距离变化
    C->>B: 应用缩放变换
    B-->>U: 响应触摸缩放
```

### 简单类图测试 - 基础功能验证

```mermaid
classDiagram
    class MermaidEnhancer {
        +Map~String,ZoomState~ zoomState
        +HTMLElement currentModal
        +init()
        +addEnhancedFeatures()
        +handleControlAction(action, svg, id)
        +zoomDiagram(svg, id, scale)
        +openFullscreenModal(svg, id)
    }
    
    class ZoomState {
        +Float scale
        +Float translateX
        +Float translateY
        +String originalViewBox
    }
    
    class ControlOverlay {
        +HTMLElement container
        +createControls()
        +bindEvents()
        +show()
        +hide()
    }
    
    class ModalSystem {
        +HTMLElement modal
        +HTMLElement backdrop
        +HTMLElement content
        +create(svg)
        +setupEventListeners()
        +close()
    }
    
    class TouchHandler {
        +Float lastTouchDistance
        +handlePinchZoom(event)
        +handleTouchMove(event)
    }
    
    MermaidEnhancer --> ZoomState : manages
    MermaidEnhancer --> ControlOverlay : creates
    MermaidEnhancer --> ModalSystem : uses
    MermaidEnhancer --> TouchHandler : integrates
    ControlOverlay --> MermaidEnhancer : triggers actions
    ModalSystem --> ZoomState : maintains separate state
```

## 🧪 测试说明

### 预期效果验证：

1. **内联控件测试**：
   - 鼠标悬停时应显示半透明的控件栏
   - 点击放大/缩小按钮应能缩放图表
   - 滚轮缩放应该平滑响应
   - 移动端应始终显示控件

2. **全屏模态框测试**：
   - 点击全屏按钮应打开模态框
   - 模态框应居中显示且有背景遮罩
   - 控件栏应包含所有功能按钮
   - 图表应自动适应模态框大小

3. **交互功能测试**：
   - 拖拽功能应能移动图表位置
   - 缩放控件应精确控制缩放级别
   - 适应窗口应智能调整图表大小
   - ESC键和点击背景应能关闭模态框

4. **响应式测试**：
   - 移动端应有触摸友好的控件
   - 双指缩放应在移动端正常工作
   - 不同屏幕尺寸应有相应的UI调整

### 🎯 交互提示：

- **鼠标用户**：悬停查看控件，滚轮缩放，拖拽移动
- **触摸用户**：双指缩放，点击控件，拖拽移动
- **键盘用户**：ESC关闭，Ctrl+/-缩放，Ctrl+0重置

如果所有功能都正常工作，说明 Mermaid 增强功能集成成功！ 🎉
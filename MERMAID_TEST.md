# Mermaid 功能测试

这是一个简单的测试文件，用于验证 Mermaid 图表渲染功能。

## 基本流程图测试

```mermaid
graph TD
    A[开始] --> B{检查配置}
    B -->|已启用| C[加载 Mermaid.js]
    B -->|未启用| D[跳过渲染]
    C --> E[检测图表代码]
    E --> F[渲染图表]
    F --> G[显示结果]
    D --> H[结束]
    G --> H
```

## 序列图测试

```mermaid
sequenceDiagram
    participant U as 用户
    participant B as 浏览器
    participant S as 服务器
    
    U->>B: 访问页面
    B->>S: 请求页面内容
    S-->>B: 返回HTML
    B->>B: 检测Mermaid代码
    B->>B: 加载Mermaid.js
    B->>B: 渲染图表
    B-->>U: 显示完整页面
```

## 简单类图测试

```mermaid
classDiagram
    class MermaidRenderer {
        +config: Object
        +init()
        +loadLibrary()
        +renderDiagrams()
    }
    
    class ThemeConfig {
        +enable: Boolean
        +theme: String
    }
    
    MermaidRenderer --> ThemeConfig
```

## 测试说明

如果您看到上面的图表正确渲染，说明 Mermaid 集成功能工作正常！

### 预期效果：
- 流程图应该显示完整的决策流程
- 序列图应该显示用户与系统的交互
- 类图应该显示类之间的关系

### 如果图表没有显示：
1. 检查主题配置中是否启用了 Mermaid
2. 确认浏览器控制台没有错误信息
3. 验证网络连接是否正常（需要加载CDN资源）
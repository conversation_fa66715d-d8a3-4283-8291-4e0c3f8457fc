extends widget/base

block append head
    link(rel='stylesheet', href= url_for("/style/widget-post-list.css"))

block content
    include widget/widget-post-list
        block sidebar
            include widget/widget-search
            if theme.widgets.includes('profile')
                include widget/widget-profile
            if theme.widgets.includes('recent_posts')
                include widget/widget-recent
            if theme.widgets.includes('category') && site.categories.length > 0
                include widget/widget-categories
            if theme.widgets.includes('archive')
                include widget/widget-archives
            if theme.widgets.includes('tag') && site.tags.length > 0
                include widget/widget-tag
            if theme.friend_links && theme.friend_links.length > 0
                main.aside-card-container.friend-widget
                    h3= _p('friends')
                    section
                        each link in theme.friend_links
                            a(href= link.link)
                                span.tag.post-item-tag(style="margin-bottom: 5px;")= link.title

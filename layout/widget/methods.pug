-
    function getPostCoverImg(post) {
        const $coverImgElRegx = /<img.*?alt="\$cover".*?>/
        const firstImgElRegx = /<img[^>]+src="?([^"\s]+)".*?>/
        const coverImgElement = post.content.match(firstImgElRegx)

        if (!coverImgElement) return false

        // post.content = post.content.replace(firstImgElRegx, `<img id="__hexo_theme_cover__" src="${coverImgElement[1]}" alt=".." style="display: none;">`)

        return coverImgElement[1]
        // const coverImg = coverImgElement[0]
        // const matchImg = coverImg.match()
        // if (matchImg) {
        //     return matchImg[1]
        // }
    }

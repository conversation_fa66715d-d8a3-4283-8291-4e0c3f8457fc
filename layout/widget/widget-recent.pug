include methods
main.aside-card-container.recent-widget
    h3= __('recent')
    ul
        if page.posts.length > 0
            each post in page.posts.sort('date', -1).limit(6).toArray()
                li.is-flex
                    - var title = post.title || __('title')
                    // change to element replace image placeholder
                    - var backgroundColor = 'f5f5f5'
                    - var uiAvatarURL = `background=${backgroundColor}&name=${title.slice(0, 2)}`
                    img.js-img-fadeIn(src= getPostCoverImg(post) || `https://ui-avatars.com/api/?${uiAvatarURL}` alt="cover")
                    //else
                    //    div.post-img-placeholder
                    section.is-flex-grow-2
                        p.has-text-weight-semibold(style= "line-height: 20px; font-size: 14px")
                            a(href=url_for(post.path))= post.title || __('title')
                        if post.date
                            time(datetime=post.date.toJSON()).has-text-weight-semibold.has-text-grey
                                = date(post.date, date_format)

style.
    .search-widget .search-input {
        border: none;
        outline: none;
        background: transparent;
        color: var(--second-text-color);
    }
    .search-widget .search-content {
        position: absolute;
        left: 0;
        top: calc(100% - 3px);
        z-index: 2;

        width: 100%;
        height: 0;
        max-height: 550px;

        overflow: auto;
        box-sizing: border-box;

        background: var(--top-bar-bg-color);
        backdrop-filter: blur(var(--backdropFilter));
        -webkit-backdrop-filter: blur(var(--backdropFilter));

        border-bottom-left-radius: var(--borderRadius);
        border-bottom-right-radius: var(--borderRadius);
        box-shadow: 0 12px 15px rgba(0, 0, 0, 0.08);
    }

    .search-widget .search-content a:hover h5 {
        color: #3273dc!important;
    }

main.aside-card-container.search-widget.is-relative
    label(for="searchInput")
        div#searchButton.is-flex.px-4
                i.iconfont.icon--search1.mr-1
                input#searchInput.search-input.is-flex-grow-1(placeholder= _p('search_input_placeholder'))
    section#searchContent.search-content.content

script.
    var searchDatabase = []
    var searchInputEl = document.getElementById('searchInput')
    var searchButtonEl = document.getElementById('searchButton')
    var searchResultEl = document.getElementById('searchContent')

    searchInputEl.oninput = function (evt) {
        var searchValue = evt.srcElement.value
        var haveSearchValue = Boolean(searchValue.trim())
        if (!haveSearchValue) {
            searchResultEl.style.height = 0
            searchResultEl.innerHTML = null
            return
        }

        var searchResults = searching(searchValue)

        if (searchResults.length > 0) {
            renderSearchResults(searchResults)
        }
    }

    function renderSearchResults(results) {
        searchResultEl.innerHTML = null
        var fragment = document.createDocumentFragment()

        results.forEach(function (item) {
            var link = document.createElement('a')
            var title = document.createElement('h5')
            var content = document.createElement('p')

            title.className = 'mb-1'
            title.innerText = item.title
            content.innerText = item.content

            link.href = item.link
            link.appendChild(title)
            link.appendChild(content)
            link.className = 'p-4 is-block'

            fragment.appendChild(link)
        })

        searchResultEl.appendChild(fragment)
        searchResultEl.style.height = 'auto'
    }

    function searching(inputText) {
        var inputTexts = inputText.split(' ')
        var searchResults = []
        inputTexts.forEach(function (searchKey) {
            var haveSearchValue = Boolean(searchKey.trim())
            if (!haveSearchValue) return

            var key = searchKey.toLowerCase()

            for (var entry of searchDatabase) {
                var title = entry.getElementsByTagName('title')[0].textContent
                var link = entry.getElementsByTagName('link')[0].getAttribute('href')
                var contentWithTags = entry.getElementsByTagName('content')[0].textContent
                var rawContent = contentWithTags.trim().replace(/<[^>]+>/g, '').toLowerCase()

                var LENGTH = 80
                var finalContent = ''
                var contentLength = rawContent.length
                var searchResultIdx = rawContent.indexOf(key)

                var startIdx = searchResultIdx - 20,
                    endIdx = startIdx + LENGTH

                if (startIdx < 0) {
                    startIdx = 0
                    endIdx = 100
                }

                endIdx > contentLength && (endIdx = contentLength)

                finalContent = rawContent.substring(startIdx, endIdx)

                if (title.indexOf(key) > -1 || searchResultIdx > -1) {
                    searchResults.push({
                        link: link,
                        title: title,
                        content: finalContent
                    })
                }
            }
        })
        return searchResults
    }

    searchButtonEl.onclick = function () {
        if (searchDatabase.length > 0) return;

        fetch(window.location.href + '/search.xml').then(res => res.text()).then(res => {
            var domparser = new DOMParser
            var doc = domparser.parseFromString(res, 'application/xml')
            searchDatabase = doc.getElementsByTagName('search')[0].children
        })
    }


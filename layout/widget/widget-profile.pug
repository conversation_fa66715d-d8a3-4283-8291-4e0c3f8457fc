main.aside-card-container.profile-widget
    // todo: 使用取色工具动态阴影
    section.is-flex.is-flex-direction-column.is-justify-content-center.is-align-items-center
        section.is-flex.is-justify-content-center.avatar.is-clipped(class= theme.user && theme.user.avatar && 'skeleton')
            if (theme.user && theme.user.avatar)
                // debug images "https://api.ixiaowai.cn/gqapi/gqapi.php"
                img.js-img-fadeIn(src= url_for(theme.user.avatar) alt="user avatar")
            else
                div.avatar-placeholder
        if (theme.user)
            h3.user-name= theme.user.name || config.author || ''
            if theme.user.description
                blockquote.has-text-centered.is-relative
                    span(style="margin-bottom: 5px;")= theme.user.description
            if (theme.user.location)
                address.has-text-centered.has-text-grey
                    i.iconfont.icon-location(style="margin-right: 5px;")
                    span.has-text-grey= theme.user.location
    section.sns-container.is-flex.is-justify-content-center.is-align-items-center
        include widget-sns

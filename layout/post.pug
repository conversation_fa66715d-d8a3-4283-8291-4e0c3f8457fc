extends widget/base
block append head
  link(rel='stylesheet', href= url_for('/style/post.css'))
  link(rel='stylesheet', href= url_for('/style/themes/highlight-theme-light.css'))
  link(rel='stylesheet', href= url_for('/style/common/jquery.fancybox.min.css'))
  script(src= url_for("/js/highlight.pack.js"))
  meta(name="description", content=truncate( strip_html(page.content), {length: 360, omission: '..'} ))
  if theme.comment_valine && theme.comment_valine.enable
      script(src="//unpkg.com/valine/dist/Valine.min.js")
block topic
    div#postTopic.is-full-height
        - var title = truncate( page.title, {length: 65, omission: '..'} ) || ''
        p.is-full-height.is-flex-shrink-0.is-flex.is-align-items-center.is-justify-content-center= title
        p.is-full-height.is-flex-shrink-0.is-flex.is-align-items-center.is-justify-content-center= __('click_back_to_the_top')

block content
    - var tocContent = toc(page.content, {list_number: false})
    main.container.is-max-widescreen.content.section.post-page.pt-4.px-4
        .columns.is-flex-desktop.is-justify-content-center.is-flex-direction-row-reverse
            .column.is-3.is-hidden-mobile(class= tocContent.length < 1 && 'is-hidden')!= tocContent
            .column.is-9
                header.my-4
                    if page.tags && page.tags.length > 0
                        each tag in page.tags.toArray()
                            a(href= url_for(`/tags/${tag.name}`))
                                i.tag.post-item-tag= tag.name
                h1.mt-0.mb-1.is-family-serif#postTitle= page.title
                if page.date
                    time.has-text-grey(datetime=page.date.toJSON())= date(page.date, date_format)
                article.mt-2.post-content!= page.content
                section.jump-container.is-flex.is-justify-content-space-between.my-6
                    // em is empty placeholder
                    if page.prev
                        - var preContent= `${ __('prev') }: ${ page.prev.title }`
                        a.button.is-default(href= url_for(page.prev.path) title= page.prev.title)
                            i.iconfont.icon-prev.mr-2.has-text-grey
                            span.has-text-weight-semibold= preContent
                    else
                        em
                    if page.next
                        - var nextContent= `${ __('next') }: ${ page.next.title }`
                        a.button.is-default(href= url_for(page.next.path) title= page.next.title)
                            span.has-text-weight-semibold= nextContent
                            i.iconfont.icon-next.ml-2.has-text-grey
                if theme.comment_utteranc && theme.comment_utteranc.enable
                    article.mt-6.comment-container
                        script(
                            async
                            repo= theme.comment_utteranc.repo
                            src= "https://utteranc.es/client.js"
                            label= theme.comment_utteranc.label
                            issue-term= theme.comment_utteranc.issue_term || "pathname"
                            theme= theme.comment_utteranc.theme || "preferred-color-scheme"
                        )
                if theme.comment_valine && theme.comment_valine.enable
                    article.mt-6.comment-container#vcomments(
                        data-comment_valine_id= theme.comment_valine.appId
                        data-comment_valine_key= theme.comment_valine.appKey
                    )
                if theme.comment_disqus && theme.comment_disqus.enable
                    div#disqus_thread
                    article.mt-6.comment-container#disqus
                        script="var disqus_config = function () {this.page.url = '"+theme.comment_disqus.url+url_for(page.path)+"';this.page.identifier = '"+theme.comment_disqus.name+"';};"
                        script="(function() {var d = document, s = d.createElement('script');s.src = 'https://"+theme.comment_disqus.name+".disqus.com/embed.js';s.setAttribute('data-timestamp', +new Date());(d.head || d.body).appendChild(s);})();"
                        script(id="dsq-count-scr", src="//blog-pubgj2togw.disqus.com/count.js", async)
block script
    if theme.image_zoom && theme.image_zoom.enable
        script(src= url_for("/js/jquery-3.6.1.min.js"))
        script(src= url_for("/js/jquery-fancybox.min.js"))
        script(src= url_for("/js/img_zoom.js"))
    script(src= url_for("/js/post.js"))

/**
 * Mermaid.js integration for Claudia theme
 * Automatically detects and renders Mermaid diagrams in posts
 */

(function() {
    'use strict';

    // Check if Mermaid is enabled in theme config
    if (!window.CLAUDIA_CONFIG || !window.CLAUDIA_CONFIG.mermaid || !window.CLAUDIA_CONFIG.mermaid.enable) {
        return;
    }

    // Default Mermaid configuration
    const defaultConfig = {
        theme: window.CLAUDIA_CONFIG.mermaid.theme || 'default',
        startOnLoad: false,
        themeVariables: {
            primaryColor: '#409eff',
            primaryTextColor: '#fff',
            primaryBorderColor: '#409eff',
            lineColor: '#409eff',
            secondaryColor: '#ecf5ff',
            tertiaryColor: '#f5f7fa'
        },
        flowchart: {
            useMaxWidth: true,
            htmlLabels: true,
            curve: 'basis'
        },
        sequence: {
            useMaxWidth: true,
            diagramMarginX: 50,
            diagramMarginY: 10,
            actorMargin: 50,
            width: 150,
            height: 65,
            boxMargin: 10,
            boxTextMargin: 5,
            noteMargin: 10,
            messageMargin: 35,
            mirrorActors: true,
            bottomMarginAdj: 1,
            useMaxWidth: true,
            rightAngles: false,
            showSequenceNumbers: false
        },
        gantt: {
            useMaxWidth: true,
            leftPadding: 75,
            rightPadding: 20,
            gridLineStartPadding: 230,
            fontSize: 11,
            fontFamily: '"Open Sans", sans-serif',
            numberSectionStyles: 4
        }
    };

    // Function to initialize Mermaid
    function initMermaid() {
        if (typeof mermaid === 'undefined') {
            console.warn('Mermaid library not loaded');
            return;
        }

        // Initialize Mermaid with configuration
        mermaid.initialize(defaultConfig);

        // Find all Mermaid code blocks
        const mermaidBlocks = document.querySelectorAll('pre code.language-mermaid, pre code.mermaid, .mermaid');
        
        if (mermaidBlocks.length === 0) {
            return;
        }

        // Process each Mermaid block
        mermaidBlocks.forEach((block, index) => {
            const isCodeBlock = block.tagName.toLowerCase() === 'code';
            // Always get raw text to avoid any inline HTML wrappers from highlighters
            const content = block.textContent || '';
            
            // Create container for the diagram
            const diagramId = `mermaid-diagram-${index}`;
            const container = document.createElement('div');
            container.className = 'mermaid-container';
            
            const diagramDiv = document.createElement('div');
            diagramDiv.id = diagramId;
            diagramDiv.className = 'mermaid-diagram';
            diagramDiv.innerHTML = content;
            // Store original content for theme switching
            diagramDiv.setAttribute('data-original-content', content);
            
            container.appendChild(diagramDiv);
            
            // Replace the original block
            if (isCodeBlock) {
                const preElement = block.closest('pre');
                if (preElement && preElement.parentNode) {
                    preElement.parentNode.replaceChild(container, preElement);
                } else if (block.parentNode) {
                    // Fallback: replace the code block itself if <pre> is not found
                    block.parentNode.replaceChild(container, block);
                } else {
                    // As a last resort, insert after the block
                    diagram.appendChild(container);
                }
            } else {
                if (block.parentNode) {
                    block.parentNode.replaceChild(container, block);
                }
            }
        });

        // Render all diagrams
        try {
            mermaid.init(undefined, '.mermaid-diagram');
        } catch (error) {
            console.error('Mermaid rendering error:', error);
        }

        // Enhance with inline controls and fullscreen modal
        try {
            // Defer enhancement slightly to allow SVGs to be inserted
            setTimeout(() => {
                enhanceInlineDiagrams();
                setupMermaidModal();
            }, 50);
        } catch (e) {
            console.error('Mermaid enhancement error:', e);
        }
    }

    // Function to load Mermaid library dynamically
    function loadMermaid() {
        return new Promise((resolve, reject) => {
            if (typeof mermaid !== 'undefined') {
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js';
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    // Initialize when DOM is ready
    function init() {
        // Check if we're on a post page or if there are mermaid blocks
        const hasMermaidContent = document.querySelector('pre code.language-mermaid, pre code.mermaid, .mermaid');
        
        if (!hasMermaidContent) {
            return;
        }

        loadMermaid()
            .then(initMermaid)
            .catch(error => {
                console.error('Failed to load Mermaid:', error);
            });
    }

    // Start initialization
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    /**
     * Add inline zoom controls and fullscreen button to each diagram
     */
    function enhanceInlineDiagrams() {
        const containers = document.querySelectorAll('.mermaid-container');
        containers.forEach((container) => {
            // Avoid duplicate setup
            if (container.getAttribute('data-enhanced') === 'true') return;

            const diagram = container.querySelector('.mermaid-diagram');
            if (!diagram) return;

            // Wrap SVG with viewport/content wrappers for transform scaling
            const svg = diagram.querySelector('svg');
            if (!svg) {
                // Retry enhancement a few times until SVG present
                const attempts = parseInt(container.getAttribute('data-enhance-attempts') || '0', 10);
                if (attempts < 10) {
                    container.setAttribute('data-enhance-attempts', String(attempts + 1));
                    setTimeout(() => enhanceInlineDiagrams(), 100);
                }
                return;
            }

            // Skip if already wrapped
            if (!diagram.querySelector('.mermaid-inline-viewport')) {
                const viewport = document.createElement('div');
                viewport.className = 'mermaid-inline-viewport';
                const content = document.createElement('div');
                content.className = 'mermaid-inline-content';

                // Move svg into content
                content.appendChild(svg);
                // Move any defs or sibling nodes too (safety)
                Array.from(diagram.childNodes).forEach((node) => {
                    if (node.tagName && node.tagName.toLowerCase() === 'svg') return;
                    if (node.nodeType === 1 || node.nodeType === 3) {
                        content.appendChild(node);
                    }
                });

                viewport.appendChild(content);
                diagram.appendChild(viewport);
            }

            // Build toolbar (zoom in / zoom out / fullscreen)
            const toolbar = document.createElement('div');
            toolbar.className = 'mermaid-toolbar';

            const btnZoomOut = createIconButton('−', 'Zoom out');
            const btnZoomIn = createIconButton('+', 'Zoom in');
            const btnFullscreen = createIconButton('⛶', 'Open fullscreen');

            toolbar.appendChild(btnZoomOut);
            toolbar.appendChild(btnZoomIn);
            toolbar.appendChild(btnFullscreen);
            container.appendChild(toolbar);

            const contentEl = container.querySelector('.mermaid-inline-content');
            const viewportEl = container.querySelector('.mermaid-inline-viewport');

            // Helpers
            const applyScale = (scale) => {
                scale = Math.min(4, Math.max(0.5, scale));
                container.dataset.scale = String(scale);
                if (contentEl) {
                    contentEl.style.transform = `scale(${scale})`;
                    contentEl.style.transformOrigin = '0 0';
                }
                if (viewportEl) {
                    viewportEl.scrollLeft = 0;
                    viewportEl.scrollTop = 0;
                }
            };

            // Init scale
            applyScale(1);
            container.dataset.scale = '1';
            container.setAttribute('data-enhanced', 'true');

            btnZoomIn.addEventListener('click', () => {
                const current = parseFloat(container.dataset.scale || '1');
                applyScale(current + 0.25);
            });
            btnZoomOut.addEventListener('click', () => {
                const current = parseFloat(container.dataset.scale || '1');
                applyScale(current - 0.25);
            });

            btnFullscreen.addEventListener('click', () => {
                openMermaidModal(diagram);
            });
        });
    }

    function createIconButton(text, title) {
        const btn = document.createElement('button');
        btn.className = 'mermaid-btn';
        btn.type = 'button';
        btn.title = title;
        btn.setAttribute('aria-label', title);
        btn.textContent = text;
        return btn;
    }

    /**
     * Create a single modal instance for fullscreen viewing
     */
    function setupMermaidModal() {
        if (document.getElementById('mermaidModal')) return;

        const overlay = document.createElement('div');
        overlay.id = 'mermaidModal';
        overlay.className = 'mermaid-modal';
        overlay.innerHTML = `
            <div class="mermaid-modal-backdrop" data-close="true"></div>
            <div class="mermaid-modal-dialog" role="dialog" aria-modal="true" aria-label="Mermaid diagram viewer">
                <div class="mermaid-modal-toolbar">
                    <button type="button" class="mermaid-btn" data-action="zoom-out" title="Zoom out" aria-label="Zoom out">−</button>
                    <button type="button" class="mermaid-btn" data-action="zoom-in" title="Zoom in" aria-label="Zoom in">+</button>
                    <button type="button" class="mermaid-btn" data-action="reset" title="Reset zoom" aria-label="Reset zoom">⟲</button>
                    <button type="button" class="mermaid-btn" data-action="fit" title="Fit to window" aria-label="Fit to window">⤢</button>
                    <button type="button" class="mermaid-btn close" data-action="close" title="Close" aria-label="Close">✕</button>
                </div>
                <div class="mermaid-modal-viewport">
                    <div class="mermaid-modal-content"></div>
                </div>
            </div>
        `;
        document.body.appendChild(overlay);

        // Events
        overlay.addEventListener('click', (e) => {
            const target = e.target;
            if (!(target instanceof Element)) return;
            if (target.getAttribute('data-close') === 'true' || target.getAttribute('data-action') === 'close') {
                closeMermaidModal();
            }
        });

        window.addEventListener('keydown', (e) => {
            const modal = document.getElementById('mermaidModal');
            if (!modal) return;
            const isOpen = modal.classList.contains('open');
            if (!isOpen) return;
            if (e.key === 'Escape') {
                closeMermaidModal();
            }
        });

        // Toolbar actions
        const dialog = overlay.querySelector('.mermaid-modal-dialog');
        if (dialog) {
            dialog.addEventListener('click', (e) => {
                const target = e.target;
                if (!(target instanceof Element)) return;
                const action = target.getAttribute('data-action');
                if (!action) return;
                if (action === 'zoom-in') modalZoomBy(0.25);
                if (action === 'zoom-out') modalZoomBy(-0.25);
                if (action === 'reset') modalSetScale(1);
                if (action === 'fit') modalFitToWindow();
            });
        }
    }

    function openMermaidModal(sourceDiagram) {
        const modal = document.getElementById('mermaidModal');
        if (!modal) return;
        const viewport = modal.querySelector('.mermaid-modal-viewport');
        const content = modal.querySelector('.mermaid-modal-content');
        if (!viewport || !content) return;

        // Reset previous content and scale
        content.innerHTML = '';
        modalSetScale(1, true);

        // Clone the current SVG into modal
        const svg = sourceDiagram.querySelector('svg');
        if (svg) {
            const cloned = svg.cloneNode(true);
            content.appendChild(cloned);
        } else {
            // Fallback: render from stored original content
            const raw = sourceDiagram.getAttribute('data-original-content') || '';
            const pre = document.createElement('pre');
            pre.textContent = raw;
            content.appendChild(pre);
        }

        modal.classList.add('open');
        // Defer fit slightly to ensure layout ready
        setTimeout(() => modalFitToWindow(), 50);
    }

    function closeMermaidModal() {
        const modal = document.getElementById('mermaidModal');
        if (!modal) return;
        modal.classList.remove('open');
    }

    function modalGetState() {
        const modal = document.getElementById('mermaidModal');
        if (!modal) return null;
        const viewport = modal.querySelector('.mermaid-modal-viewport');
        const content = modal.querySelector('.mermaid-modal-content');
        return { modal, viewport, content };
    }

    function modalSetScale(scale, skipClamp) {
        const state = modalGetState();
        if (!state) return;
        const { content } = state;
        if (!content) return;
        if (!skipClamp) scale = Math.min(6, Math.max(0.25, scale));
        content.style.transform = `scale(${scale})`;
        content.style.transformOrigin = '0 0';
        content.setAttribute('data-scale', String(scale));
    }

    function modalZoomBy(delta) {
        const state = modalGetState();
        if (!state) return;
        const { content } = state;
        if (!content) return;
        const current = parseFloat(content.getAttribute('data-scale') || '1');
        modalSetScale(current + delta);
    }

    function modalFitToWindow() {
        const state = modalGetState();
        if (!state) return;
        const { viewport, content } = state;
        if (!viewport || !content) return;

        const svg = content.querySelector('svg');
        if (!svg) {
            modalSetScale(1);
            return;
        }

        const viewportRect = viewport.getBoundingClientRect();
        let svgWidth = 0;
        let svgHeight = 0;

        // Prefer viewBox when available
        const viewBox = svg.getAttribute('viewBox');
        if (viewBox) {
            const parts = viewBox.split(/[ ,]+/).map(Number);
            if (parts.length === 4) {
                svgWidth = parts[2];
                svgHeight = parts[3];
            }
        }
        if (!svgWidth || !svgHeight) {
            const bbox = svg.getBBox ? svg.getBBox() : null;
            if (bbox && bbox.width && bbox.height) {
                svgWidth = bbox.width;
                svgHeight = bbox.height;
            } else {
                const rect = svg.getBoundingClientRect();
                svgWidth = rect.width || 1;
                svgHeight = rect.height || 1;
            }
        }

        const scale = Math.max(0.1, Math.min(viewportRect.width / svgWidth, viewportRect.height / svgHeight));
        modalSetScale(scale, true);
        // Centering handled by CSS flex on dialog viewport if needed
    }

    // Support for theme switching
    function handleThemeChange() {
        if (typeof mermaid !== 'undefined') {
            // Detect current theme
            const isDark = document.documentElement.classList.contains('dark') || 
                          document.body.classList.contains('dark') ||
                          window.matchMedia('(prefers-color-scheme: dark)').matches;
            
            const newTheme = isDark ? 'dark' : 'default';
            if (defaultConfig.theme !== newTheme) {
                defaultConfig.theme = newTheme;
                mermaid.initialize(defaultConfig);
                
                // Re-render existing diagrams
                const diagrams = document.querySelectorAll('.mermaid-diagram');
                diagrams.forEach(diagram => {
                    diagram.removeAttribute('data-processed');
                    diagram.innerHTML = diagram.getAttribute('data-original-content') || diagram.innerHTML;
                });
                
                try {
                    mermaid.init(undefined, '.mermaid-diagram');
                } catch (error) {
                    console.error('Mermaid theme switch error:', error);
                }
            }
        }
    }
    
    // Listen for theme changes
    window.addEventListener('theme-changed', handleThemeChange);
    
    // Listen for system theme changes
    if (window.matchMedia) {
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', handleThemeChange);
    }

})();
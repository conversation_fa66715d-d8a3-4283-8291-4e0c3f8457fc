/**
 * Mermaid.js integration for Claudia theme
 * Automatically detects and renders Mermaid diagrams in posts
 */

(function() {
    'use strict';

    // Check if Mermaid is enabled in theme config
    if (!window.CLAUDIA_CONFIG || !window.CLAUDIA_CONFIG.mermaid || !window.CLAUDIA_CONFIG.mermaid.enable) {
        return;
    }

    // Default Mermaid configuration
    const defaultConfig = {
        theme: window.CLAUDIA_CONFIG.mermaid.theme || 'default',
        startOnLoad: false,
        themeVariables: {
            primaryColor: '#409eff',
            primaryTextColor: '#fff',
            primaryBorderColor: '#409eff',
            lineColor: '#409eff',
            secondaryColor: '#ecf5ff',
            tertiaryColor: '#f5f7fa'
        },
        flowchart: {
            useMaxWidth: true,
            htmlLabels: true,
            curve: 'basis'
        },
        sequence: {
            useMaxWidth: true,
            diagramMarginX: 50,
            diagramMarginY: 10,
            actorMargin: 50,
            width: 150,
            height: 65,
            boxMargin: 10,
            boxTextMargin: 5,
            noteMargin: 10,
            messageMargin: 35,
            mirrorActors: true,
            bottomMarginAdj: 1,
            useMaxWidth: true,
            rightAngles: false,
            showSequenceNumbers: false
        },
        gantt: {
            useMaxWidth: true,
            leftPadding: 75,
            rightPadding: 20,
            gridLineStartPadding: 230,
            fontSize: 11,
            fontFamily: '"Open Sans", sans-serif',
            numberSectionStyles: 4
        }
    };

    // Helper function to create buttons
    function createButton(className, text, title) {
        const button = document.createElement('button');
        button.className = `mermaid-btn ${className}`;
        button.innerHTML = text;
        button.title = title;
        button.type = 'button';
        return button;
    }

    // Zoom functionality
    function setupZoomControls(container) {
        const diagramWrapper = container.querySelector('.mermaid-diagram-wrapper');
        const diagram = container.querySelector('.mermaid-diagram');
        let currentZoom = 1;
        const minZoom = 0.5;
        const maxZoom = 3;
        const zoomStep = 0.25;

        function applyZoom(zoom) {
            currentZoom = Math.max(minZoom, Math.min(maxZoom, zoom));
            diagram.style.transform = `scale(${currentZoom})`;
            diagram.style.transformOrigin = 'center center';
            
            // Update wrapper size to prevent overflow
            if (currentZoom !== 1) {
                diagramWrapper.style.overflow = 'auto';
                diagramWrapper.style.padding = '20px';
            } else {
                diagramWrapper.style.overflow = 'visible';
                diagramWrapper.style.padding = '0';
            }
        }

        // Zoom in
        container.querySelector('.zoom-in').addEventListener('click', () => {
            applyZoom(currentZoom + zoomStep);
        });

        // Zoom out
        container.querySelector('.zoom-out').addEventListener('click', () => {
            applyZoom(currentZoom - zoomStep);
        });

        // Reset zoom
        container.querySelector('.zoom-reset').addEventListener('click', () => {
            applyZoom(1);
        });

        // Fullscreen
        container.querySelector('.fullscreen').addEventListener('click', () => {
            openFullscreenModal(container);
        });

        // Mouse wheel zoom
        diagramWrapper.addEventListener('wheel', (e) => {
            if (e.ctrlKey || e.metaKey) {
                e.preventDefault();
                const delta = e.deltaY > 0 ? -zoomStep : zoomStep;
                applyZoom(currentZoom + delta);
            }
        });
    }

    // Fullscreen modal functionality
    function openFullscreenModal(container) {
        const originalDiagram = container.querySelector('.mermaid-diagram');
        const originalContent = originalDiagram.getAttribute('data-original-content');
        
        // Create modal
        const modal = document.createElement('div');
        modal.className = 'mermaid-modal';
        modal.innerHTML = `
            <div class="mermaid-modal-backdrop"></div>
            <div class="mermaid-modal-content">
                <div class="mermaid-modal-header">
                    <div class="mermaid-modal-controls">
                        <button class="mermaid-btn modal-zoom-in" title="Zoom In">🔍+</button>
                        <button class="mermaid-btn modal-zoom-out" title="Zoom Out">🔍-</button>
                        <button class="mermaid-btn modal-zoom-reset" title="Reset Zoom">⚡</button>
                        <button class="mermaid-btn modal-zoom-fit" title="Fit to Window">📐</button>
                    </div>
                    <button class="mermaid-btn modal-close" title="Close">✕</button>
                </div>
                <div class="mermaid-modal-body">
                    <div class="mermaid-modal-diagram-wrapper">
                        <div class="mermaid-diagram modal-diagram" id="modal-${originalDiagram.id}">${originalContent}</div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Render the diagram in modal
        try {
            mermaid.init(undefined, `#modal-${originalDiagram.id}`);
        } catch (error) {
            console.error('Modal Mermaid rendering error:', error);
        }

        // Setup modal controls
        setupModalControls(modal);

        // Show modal
        requestAnimationFrame(() => {
            modal.classList.add('active');
        });

        // Prevent body scroll
        document.body.style.overflow = 'hidden';
    }

    // Setup modal controls
    function setupModalControls(modal) {
        const modalDiagram = modal.querySelector('.modal-diagram');
        const modalWrapper = modal.querySelector('.mermaid-modal-diagram-wrapper');
        let modalZoom = 1;
        const modalMinZoom = 0.25;
        const modalMaxZoom = 5;
        const modalZoomStep = 0.25;

        function applyModalZoom(zoom) {
            modalZoom = Math.max(modalMinZoom, Math.min(modalMaxZoom, zoom));
            modalDiagram.style.transform = `scale(${modalZoom})`;
            modalDiagram.style.transformOrigin = 'center center';
        }

        function fitToWindow() {
            const modalBody = modal.querySelector('.mermaid-modal-body');
            const diagram = modal.querySelector('.modal-diagram svg');
            
            if (diagram) {
                const bodyRect = modalBody.getBoundingClientRect();
                const diagramRect = diagram.getBoundingClientRect();
                
                const scaleX = (bodyRect.width - 40) / diagramRect.width;
                const scaleY = (bodyRect.height - 40) / diagramRect.height;
                const fitZoom = Math.min(scaleX, scaleY, 1);
                
                applyModalZoom(fitZoom);
            }
        }

        // Modal zoom controls
        modal.querySelector('.modal-zoom-in').addEventListener('click', () => {
            applyModalZoom(modalZoom + modalZoomStep);
        });

        modal.querySelector('.modal-zoom-out').addEventListener('click', () => {
            applyModalZoom(modalZoom - modalZoomStep);
        });

        modal.querySelector('.modal-zoom-reset').addEventListener('click', () => {
            applyModalZoom(1);
        });

        modal.querySelector('.modal-zoom-fit').addEventListener('click', fitToWindow);

        // Close modal functionality
        function closeModal() {
            modal.classList.remove('active');
            document.body.style.overflow = '';
            setTimeout(() => {
                document.body.removeChild(modal);
            }, 300);
        }

        // Close button
        modal.querySelector('.modal-close').addEventListener('click', closeModal);

        // Click backdrop to close
        modal.querySelector('.mermaid-modal-backdrop').addEventListener('click', closeModal);

        // ESC key to close
        function handleKeyDown(e) {
            if (e.key === 'Escape') {
                closeModal();
                document.removeEventListener('keydown', handleKeyDown);
            }
        }
        document.addEventListener('keydown', handleKeyDown);

        // Mouse wheel zoom in modal
        modalWrapper.addEventListener('wheel', (e) => {
            if (e.ctrlKey || e.metaKey) {
                e.preventDefault();
                const delta = e.deltaY > 0 ? -modalZoomStep : modalZoomStep;
                applyModalZoom(modalZoom + delta);
            }
        });

        // Auto-fit on load
        setTimeout(fitToWindow, 100);
    }

    // Function to initialize Mermaid
    function initMermaid() {
        if (typeof mermaid === 'undefined') {
            console.warn('Mermaid library not loaded');
            return;
        }

        // Initialize Mermaid with configuration
        mermaid.initialize(defaultConfig);

        // Find all Mermaid code blocks
        const mermaidBlocks = document.querySelectorAll('pre code.language-mermaid, pre code.mermaid, .mermaid');
        
        if (mermaidBlocks.length === 0) {
            return;
        }

        // Process each Mermaid block
        mermaidBlocks.forEach((block, index) => {
            const isCodeBlock = block.tagName.toLowerCase() === 'code';
            const content = isCodeBlock ? block.textContent : block.innerHTML;
            
            // Create container for the diagram
            const diagramId = `mermaid-diagram-${index}`;
            const container = document.createElement('div');
            container.className = 'mermaid-container';
            
            // Create toolbar
            const toolbar = document.createElement('div');
            toolbar.className = 'mermaid-toolbar';
            
            // Create zoom controls
            const zoomControls = document.createElement('div');
            zoomControls.className = 'mermaid-zoom-controls';
            
            const zoomInBtn = createButton('zoom-in', '🔍+', 'Zoom In');
            const zoomOutBtn = createButton('zoom-out', '🔍-', 'Zoom Out');
            const zoomResetBtn = createButton('zoom-reset', '⚡', 'Reset Zoom');
            const fullscreenBtn = createButton('fullscreen', '⛶', 'Fullscreen');
            
            zoomControls.appendChild(zoomInBtn);
            zoomControls.appendChild(zoomOutBtn);
            zoomControls.appendChild(zoomResetBtn);
            zoomControls.appendChild(fullscreenBtn);
            
            toolbar.appendChild(zoomControls);
            
            // Create diagram wrapper
            const diagramWrapper = document.createElement('div');
            diagramWrapper.className = 'mermaid-diagram-wrapper';
            
            const diagramDiv = document.createElement('div');
            diagramDiv.id = diagramId;
            diagramDiv.className = 'mermaid-diagram';
            diagramDiv.innerHTML = content;
            // Store original content for theme switching
            diagramDiv.setAttribute('data-original-content', content);
            
            diagramWrapper.appendChild(diagramDiv);
            
            container.appendChild(toolbar);
            container.appendChild(diagramWrapper);
            
            // Replace the original block
            if (isCodeBlock) {
                const preElement = block.closest('pre');
                if (preElement) {
                    preElement.parentNode.replaceChild(container, preElement);
                }
            } else {
                block.parentNode.replaceChild(container, block);
            }
        });

        // Render all diagrams
        try {
            mermaid.init(undefined, '.mermaid-diagram');
            
            // Setup zoom controls for each container after rendering
            document.querySelectorAll('.mermaid-container').forEach(container => {
                setupZoomControls(container);
            });
        } catch (error) {
            console.error('Mermaid rendering error:', error);
        }
    }

    // Function to load Mermaid library dynamically
    function loadMermaid() {
        return new Promise((resolve, reject) => {
            if (typeof mermaid !== 'undefined') {
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js';
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    // Initialize when DOM is ready
    function init() {
        // Check if we're on a post page or if there are mermaid blocks
        const hasMermaidContent = document.querySelector('pre code.language-mermaid, pre code.mermaid, .mermaid');
        
        if (!hasMermaidContent) {
            return;
        }

        loadMermaid()
            .then(initMermaid)
            .catch(error => {
                console.error('Failed to load Mermaid:', error);
            });
    }

    // Start initialization
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // Support for theme switching
    function handleThemeChange() {
        if (typeof mermaid !== 'undefined') {
            // Detect current theme
            const isDark = document.documentElement.classList.contains('dark') || 
                          document.body.classList.contains('dark') ||
                          window.matchMedia('(prefers-color-scheme: dark)').matches;
            
            const newTheme = isDark ? 'dark' : 'default';
            if (defaultConfig.theme !== newTheme) {
                defaultConfig.theme = newTheme;
                mermaid.initialize(defaultConfig);
                
                // Re-render existing diagrams
                const diagrams = document.querySelectorAll('.mermaid-diagram');
                diagrams.forEach(diagram => {
                    diagram.removeAttribute('data-processed');
                    diagram.innerHTML = diagram.getAttribute('data-original-content') || diagram.innerHTML;
                });
                
                try {
                    mermaid.init(undefined, '.mermaid-diagram');
                } catch (error) {
                    console.error('Mermaid theme switch error:', error);
                }
            }
        }
    }
    
    // Listen for theme changes
    window.addEventListener('theme-changed', handleThemeChange);
    
    // Listen for system theme changes
    if (window.matchMedia) {
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', handleThemeChange);
    }

})();
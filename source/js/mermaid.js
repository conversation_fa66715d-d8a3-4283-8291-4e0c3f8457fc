/**
 * Enhanced Mermaid.js integration for Claudia theme
 * Features: Auto-detection, inline zoom, fullscreen modal, advanced controls
 */

(function() {
    'use strict';
    
    // State management for enhanced features
    let currentModal = null;
    let zoomState = new Map(); // Track zoom state for each diagram

    // Check if Mermaid is enabled in theme config
    if (!window.CLAUDIA_CONFIG || !window.CLAUDIA_CONFIG.mermaid || !window.CLAUDIA_CONFIG.mermaid.enable) {
        return;
    }

    // Default Mermaid configuration
    const defaultConfig = {
        theme: window.CLAUDIA_CONFIG.mermaid.theme || 'default',
        startOnLoad: false,
        themeVariables: {
            primaryColor: '#409eff',
            primaryTextColor: '#fff',
            primaryBorderColor: '#409eff',
            lineColor: '#409eff',
            secondaryColor: '#ecf5ff',
            tertiaryColor: '#f5f7fa'
        },
        flowchart: {
            useMaxWidth: true,
            htmlLabels: true,
            curve: 'basis'
        },
        sequence: {
            useMaxWidth: true,
            diagramMarginX: 50,
            diagramMarginY: 10,
            actorMargin: 50,
            width: 150,
            height: 65,
            boxMargin: 10,
            boxTextMargin: 5,
            noteMargin: 10,
            messageMargin: 35,
            mirrorActors: true,
            bottomMarginAdj: 1,
            useMaxWidth: true,
            rightAngles: false,
            showSequenceNumbers: false
        },
        gantt: {
            useMaxWidth: true,
            leftPadding: 75,
            rightPadding: 20,
            gridLineStartPadding: 230,
            fontSize: 11,
            fontFamily: '"Open Sans", sans-serif',
            numberSectionStyles: 4
        }
    };

    // Function to initialize Mermaid
    function initMermaid() {
        if (typeof mermaid === 'undefined') {
            console.warn('Mermaid library not loaded');
            return;
        }

        // Initialize Mermaid with configuration
        mermaid.initialize(defaultConfig);

        // Find all Mermaid code blocks
        const mermaidBlocks = document.querySelectorAll('pre code.language-mermaid, pre code.mermaid, .mermaid');
        
        if (mermaidBlocks.length === 0) {
            return;
        }

        // Process each Mermaid block
        mermaidBlocks.forEach((block, index) => {
            const isCodeBlock = block.tagName.toLowerCase() === 'code';
            const content = isCodeBlock ? block.textContent : block.innerHTML;
            
            // Create container for the diagram
            const diagramId = `mermaid-diagram-${index}`;
            const container = document.createElement('div');
            container.className = 'mermaid-container';
            
            const diagramDiv = document.createElement('div');
            diagramDiv.id = diagramId;
            diagramDiv.className = 'mermaid-diagram';
            diagramDiv.innerHTML = content;
            // Store original content for theme switching
            diagramDiv.setAttribute('data-original-content', content);
            
            container.appendChild(diagramDiv);
            
            // Replace the original block
            if (isCodeBlock) {
                const preElement = block.closest('pre');
                if (preElement) {
                    preElement.parentNode.replaceChild(container, preElement);
                }
            } else {
                block.parentNode.replaceChild(container, block);
            }
        });

        // Render all diagrams
        try {
            mermaid.init(undefined, '.mermaid-diagram');
            
            // Add enhanced features after rendering
            setTimeout(() => {
                addEnhancedFeatures();
            }, 100);
        } catch (error) {
            console.error('Mermaid rendering error:', error);
        }
    }

    // Enhanced features implementation
    function addEnhancedFeatures() {
        const diagrams = document.querySelectorAll('.mermaid-diagram svg');
        
        diagrams.forEach((svg, index) => {
            const container = svg.closest('.mermaid-container');
            if (!container || container.classList.contains('enhanced')) return;
            
            container.classList.add('enhanced');
            const diagramId = `mermaid-enhanced-${index}`;
            
            // Initialize zoom state
            zoomState.set(diagramId, {
                scale: 1,
                translateX: 0,
                translateY: 0,
                originalViewBox: svg.getAttribute('viewBox') || '0 0 100 100'
            });
            
            // Add control overlay
            addControlOverlay(container, svg, diagramId);
            
            // Add inline zoom functionality
            addInlineZoom(container, svg, diagramId);
        });
    }

    // Add control overlay with fullscreen button
    function addControlOverlay(container, svg, diagramId) {
        const overlay = document.createElement('div');
        overlay.className = 'mermaid-controls';
        overlay.innerHTML = `
            <div class="mermaid-inline-controls">
                <button class="mermaid-btn zoom-in" title="Zoom In" data-action="zoom-in">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                        <path d="M12 10h-2v2H9v-2H7V9h2V7h1v2h2v1z"/>
                    </svg>
                </button>
                <button class="mermaid-btn zoom-out" title="Zoom Out" data-action="zoom-out">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                        <path d="M7 9v1h5V9H7z"/>
                    </svg>
                </button>
                <button class="mermaid-btn zoom-reset" title="Reset Zoom" data-action="zoom-reset">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 5V1L7 6l5 5V7c3.31 0 6 2.69 6 6s-2.69 6-6 6-6-2.69-6-6H4c0 4.42 3.58 8 8 8s8-3.58 8-8-3.58-8-8-8z"/>
                    </svg>
                </button>
                <button class="mermaid-btn fit-to-width" title="Fit to Width" data-action="fit-to-width">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M9 12l-4.5 4.5L6 18l6-6-6-6-1.5 1.5L9 12zm6 0l4.5-4.5L18 6l-6 6 6 6 1.5-1.5L15 12z"/>
                    </svg>
                </button>
                <button class="mermaid-btn fullscreen" title="Open Fullscreen" data-action="fullscreen">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"/>
                    </svg>
                </button>
            </div>
        `;
        
        container.appendChild(overlay);
        
        // Add event listeners for controls
        overlay.addEventListener('click', (e) => {
            const action = e.target.closest('.mermaid-btn')?.dataset.action;
            if (action) {
                e.preventDefault();
                e.stopPropagation();
                handleControlAction(action, svg, diagramId, container);
            }
        });
    }

    // Handle control actions with enhanced functionality
    function handleControlAction(action, svg, diagramId, container) {
        switch (action) {
            case 'zoom-in':
                zoomDiagram(svg, diagramId, 1.2, undefined, undefined, container);
                break;
            case 'zoom-out':
                zoomDiagram(svg, diagramId, 0.8, undefined, undefined, container);
                break;
            case 'zoom-reset':
                resetZoom(svg, diagramId, container);
                break;
            case 'fit-to-width':
                fitToWidth(svg, diagramId, container);
                break;
            case 'fullscreen':
                openFullscreenModal(svg, diagramId, container);
                break;
        }
    }

    // Fit diagram to container width
    function fitToWidth(svg, diagramId, container) {
        const state = zoomState.get(diagramId);
        if (!state) return;
        
        // Reset position first
        state.translateX = 0;
        state.translateY = 0;
        
        // Get natural dimensions
        const svgRect = svg.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();
        
        // Calculate scale to fit width with some padding
        const padding = 20;
        const scaleToFitWidth = (containerRect.width - padding) / svgRect.width;
        
        // Limit scale to reasonable bounds
        const newScale = Math.max(0.1, Math.min(3, scaleToFitWidth));
        
        // Animate to new scale
        const startScale = state.scale;
        const startTranslateX = state.translateX;
        const startTranslateY = state.translateY;
        const duration = 400; // ms
        const startTime = performance.now();
        
        function animateFit(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // Use easeOutCubic for smooth animation
            const easedProgress = 1 - Math.pow(1 - progress, 3);
            
            state.scale = startScale + (newScale - startScale) * easedProgress;
            state.translateX = startTranslateX + (0 - startTranslateX) * easedProgress;
            state.translateY = startTranslateY + (0 - startTranslateY) * easedProgress;
            
            applyTransform(svg, state);
            zoomState.set(diagramId, state);
            
            if (progress < 1) {
                requestAnimationFrame(animateFit);
            } else {
                // Ensure final values are exact
                state.scale = newScale;
                state.translateX = 0;
                state.translateY = 0;
                applyTransform(svg, state);
                zoomState.set(diagramId, state);
                
                // Update cursor
                updateCursorForZoomLevel(container, state);
            }
        }
        
        requestAnimationFrame(animateFit);
    }

    // Enhanced inline zoom and pan functionality
    function addInlineZoom(container, svg, diagramId) {
        let isDragging = false;
        let isPanning = false;
        let startX, startY;
        let startTranslateX, startTranslateY;
        let animationFrame;
        
        // Mouse wheel zoom with improved center point calculation
        container.addEventListener('wheel', (e) => {
            e.preventDefault();
            
            // Cancel any ongoing animation
            if (animationFrame) {
                cancelAnimationFrame(animationFrame);
            }
            
            const delta = e.deltaY > 0 ? 0.9 : 1.1;
            const rect = container.getBoundingClientRect();
            const centerX = e.clientX - rect.left;
            const centerY = e.clientY - rect.top;
            
            animationFrame = requestAnimationFrame(() => {
                zoomDiagram(svg, diagramId, delta, centerX, centerY, container);
            });
        });
        
        // Enhanced mouse events for panning
        container.addEventListener('mousedown', (e) => {
            // Only allow panning with left mouse button and when not clicking on control buttons
            if (e.button === 0 && !e.target.closest('.mermaid-controls')) {
                const state = zoomState.get(diagramId);
                
                // Only enable panning if zoomed in
                if (state && state.scale > 1) {
                    isPanning = true;
                    startX = e.clientX;
                    startY = e.clientY;
                    startTranslateX = state.translateX;
                    startTranslateY = state.translateY;
                    
                    container.style.cursor = 'grabbing';
                    e.preventDefault();
                }
            }
        });
        
        document.addEventListener('mousemove', (e) => {
            if (isPanning) {
                if (animationFrame) {
                    cancelAnimationFrame(animationFrame);
                }
                
                const deltaX = e.clientX - startX;
                const deltaY = e.clientY - startY;
                
                animationFrame = requestAnimationFrame(() => {
                    panDiagram(svg, diagramId, deltaX, deltaY, startTranslateX, startTranslateY, container);
                });
            }
        });
        
        document.addEventListener('mouseup', () => {
            if (isPanning) {
                isPanning = false;
                container.style.cursor = '';
            }
        });
        
        // Enhanced touch zoom (pinch) with better gesture recognition
        let lastTouchDistance = 0;
        let touchStartTime = 0;
        let touchCenter = { x: 0, y: 0 };
        
        container.addEventListener('touchstart', (e) => {
            touchStartTime = Date.now();
            
            if (e.touches.length === 2) {
                const touch1 = e.touches[0];
                const touch2 = e.touches[1];
                lastTouchDistance = Math.sqrt(
                    Math.pow(touch2.clientX - touch1.clientX, 2) +
                    Math.pow(touch2.clientY - touch1.clientY, 2)
                );
                
                // Calculate center point of pinch gesture
                const rect = container.getBoundingClientRect();
                touchCenter.x = ((touch1.clientX + touch2.clientX) / 2) - rect.left;
                touchCenter.y = ((touch1.clientY + touch2.clientY) / 2) - rect.top;
            } else if (e.touches.length === 1) {
                // Single touch for potential panning
                const state = zoomState.get(diagramId);
                if (state && state.scale > 1) {
                    const touch = e.touches[0];
                    startX = touch.clientX;
                    startY = touch.clientY;
                    startTranslateX = state.translateX;
                    startTranslateY = state.translateY;
                    isPanning = true;
                }
            }
        });
        
        container.addEventListener('touchmove', (e) => {
            e.preventDefault();
            
            if (e.touches.length === 2 && lastTouchDistance > 0) {
                // Pinch zoom
                const touch1 = e.touches[0];
                const touch2 = e.touches[1];
                const currentDistance = Math.sqrt(
                    Math.pow(touch2.clientX - touch1.clientX, 2) +
                    Math.pow(touch2.clientY - touch1.clientY, 2)
                );
                
                const delta = currentDistance / lastTouchDistance;
                
                if (animationFrame) {
                    cancelAnimationFrame(animationFrame);
                }
                
                animationFrame = requestAnimationFrame(() => {
                    zoomDiagram(svg, diagramId, delta, touchCenter.x, touchCenter.y, container);
                });
                
                lastTouchDistance = currentDistance;
            } else if (e.touches.length === 1 && isPanning) {
                // Single finger pan
                const touch = e.touches[0];
                const deltaX = touch.clientX - startX;
                const deltaY = touch.clientY - startY;
                
                if (animationFrame) {
                    cancelAnimationFrame(animationFrame);
                }
                
                animationFrame = requestAnimationFrame(() => {
                    panDiagram(svg, diagramId, deltaX, deltaY, startTranslateX, startTranslateY, container);
                });
            }
        });
        
        container.addEventListener('touchend', (e) => {
            const touchDuration = Date.now() - touchStartTime;
            
            if (e.touches.length === 0) {
                isPanning = false;
                lastTouchDistance = 0;
                
                // Double tap to zoom (if tap was quick)
                if (touchDuration < 300 && e.changedTouches.length === 1) {
                    const touch = e.changedTouches[0];
                    const rect = container.getBoundingClientRect();
                    const centerX = touch.clientX - rect.left;
                    const centerY = touch.clientY - rect.top;
                    
                    // Simple double-tap detection (could be enhanced)
                    setTimeout(() => {
                        zoomDiagram(svg, diagramId, 1.5, centerX, centerY, container);
                    }, 10);
                }
            }
        });
        
        // Add visual feedback for pannable state
        container.addEventListener('mouseenter', () => {
            const state = zoomState.get(diagramId);
            if (state && state.scale > 1) {
                container.style.cursor = 'grab';
            }
        });
        
        container.addEventListener('mouseleave', () => {
            if (!isPanning) {
                container.style.cursor = '';
            }
        });
    }

    // Enhanced pan function with bounds checking
    function panDiagram(svg, diagramId, deltaX, deltaY, startTranslateX, startTranslateY, container) {
        const state = zoomState.get(diagramId);
        if (!state) return;
        
        // Calculate new translation values
        state.translateX = startTranslateX + deltaX;
        state.translateY = startTranslateY + deltaY;
        
        // Apply bounds to keep diagram partially visible
        const bounds = calculateBounds(svg, container, state);
        applyBounds(state, bounds);
        
        // Apply the transformation
        applyTransform(svg, state);
        zoomState.set(diagramId, state);
    }

    // Enhanced zoom diagram function with precise center point calculation
    function zoomDiagram(svg, diagramId, scaleFactor, centerX, centerY, container) {
        const state = zoomState.get(diagramId);
        if (!state) return;
        
        const oldScale = state.scale;
        const newScale = Math.max(0.1, Math.min(5, oldScale * scaleFactor));
        
        // If no center point provided, zoom from center of container
        if (centerX === undefined || centerY === undefined) {
            const containerRect = container.getBoundingClientRect();
            centerX = containerRect.width / 2;
            centerY = containerRect.height / 2;
        }
        
        // Calculate the point in the diagram that should remain fixed during zoom
        const fixedPointX = (centerX - state.translateX) / oldScale;
        const fixedPointY = (centerY - state.translateY) / oldScale;
        
        // Update scale
        state.scale = newScale;
        
        // Calculate new translation to keep the fixed point at the same screen position
        state.translateX = centerX - fixedPointX * newScale;
        state.translateY = centerY - fixedPointY * newScale;
        
        // Apply bounds to prevent diagram from going completely off-screen
        if (container) {
            const bounds = calculateBounds(svg, container, state);
            applyBounds(state, bounds);
        }
        
        // Apply the transformation
        applyTransform(svg, state);
        zoomState.set(diagramId, state);
        
        // Update cursor based on zoom level
        if (container) {
            updateCursorForZoomLevel(container, state);
        }
    }

    // Update cursor based on zoom level
    function updateCursorForZoomLevel(container, state) {
        if (state.scale > 1) {
            if (!container.style.cursor || container.style.cursor === '') {
                container.style.cursor = 'grab';
            }
        } else {
            if (container.style.cursor === 'grab') {
                container.style.cursor = '';
            }
        }
    }

    // Enhanced reset zoom with smooth animation
    function resetZoom(svg, diagramId, container) {
        const state = zoomState.get(diagramId);
        if (!state) return;
        
        // Animate back to original state
        const startScale = state.scale;
        const startTranslateX = state.translateX;
        const startTranslateY = state.translateY;
        const duration = 300; // ms
        const startTime = performance.now();
        
        function animateReset(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // Use easeOutCubic for smooth animation
            const easedProgress = 1 - Math.pow(1 - progress, 3);
            
            state.scale = startScale + (1 - startScale) * easedProgress;
            state.translateX = startTranslateX + (0 - startTranslateX) * easedProgress;
            state.translateY = startTranslateY + (0 - startTranslateY) * easedProgress;
            
            applyTransform(svg, state);
            zoomState.set(diagramId, state);
            
            if (progress < 1) {
                requestAnimationFrame(animateReset);
            } else {
                // Ensure final values are exact
                state.scale = 1;
                state.translateX = 0;
                state.translateY = 0;
                applyTransform(svg, state);
                zoomState.set(diagramId, state);
                
                // Update cursor
                if (container) {
                    updateCursorForZoomLevel(container, state);
                }
            }
        }
        
        requestAnimationFrame(animateReset);
    }



    // Enhanced transform application with matrix calculation
    function applyTransform(svg, state) {
        // Use matrix transformation for better precision and performance
        const matrix = `matrix(${state.scale}, 0, 0, ${state.scale}, ${state.translateX}, ${state.translateY})`;
        svg.style.transform = matrix;
        svg.style.transformOrigin = '0 0';
        svg.style.transition = 'transform 0.2s ease-out';
    }

    // Calculate optimal bounds for pan limits
    function calculateBounds(svg, container, state) {
        const svgRect = svg.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();
        
        const scaledWidth = svgRect.width * state.scale;
        const scaledHeight = svgRect.height * state.scale;
        
        // Calculate maximum translation limits to keep diagram partially visible
        const minVisiblePortion = 0.1; // Keep at least 10% visible
        const maxTranslateX = containerRect.width * (1 - minVisiblePortion);
        const minTranslateX = -(scaledWidth - containerRect.width * minVisiblePortion);
        const maxTranslateY = containerRect.height * (1 - minVisiblePortion);
        const minTranslateY = -(scaledHeight - containerRect.height * minVisiblePortion);
        
        return {
            minX: minTranslateX,
            maxX: maxTranslateX,
            minY: minTranslateY,
            maxY: maxTranslateY
        };
    }

    // Apply bounds to translation values
    function applyBounds(state, bounds) {
        state.translateX = Math.max(bounds.minX, Math.min(bounds.maxX, state.translateX));
        state.translateY = Math.max(bounds.minY, Math.min(bounds.maxY, state.translateY));
        return state;
    }

    // Fullscreen modal functionality
    function openFullscreenModal(svg, diagramId, container) {
        // Create modal if it doesn't exist
        let modal = document.getElementById('mermaid-fullscreen-modal');
        if (!modal) {
            modal = createFullscreenModal();
        }
        
        // Clone the SVG for the modal
        const modalBody = modal.querySelector('.mermaid-modal-body');
        const modalDiagram = modal.querySelector('.mermaid-modal-diagram');
        const clonedSvg = svg.cloneNode(true);
        
        // Reset transforms for modal
        clonedSvg.style.transform = '';
        clonedSvg.style.transformOrigin = 'center center';
        
        // Clear previous content and add new diagram
        modalDiagram.innerHTML = '';
        modalDiagram.appendChild(clonedSvg);
        
        // Initialize modal zoom state
        const modalId = 'modal-' + diagramId;
        zoomState.set(modalId, {
            scale: 1,
            translateX: 0,
            translateY: 0,
            originalViewBox: clonedSvg.getAttribute('viewBox') || '0 0 100 100'
        });
        
        // Store current modal diagram ID
        modal.dataset.currentDiagram = modalId;
        
        // Show modal
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
        
        // Fit to window after a brief delay
        setTimeout(() => fitToWindow(clonedSvg, modalId), 100);
    }

    function createFullscreenModal() {
        const modal = document.createElement('div');
        modal.id = 'mermaid-fullscreen-modal';
        modal.className = 'mermaid-modal';
        modal.innerHTML = `
            <div class="mermaid-modal-overlay"></div>
            <div class="mermaid-modal-content">
                <div class="mermaid-modal-header">
                    <div class="mermaid-modal-controls">
                        <button class="mermaid-modal-btn zoom-in" title="Zoom In (Ctrl+)" data-action="zoom-in">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                                <path d="M12 10h-2v2H9v-2H7V9h2V7h1v2h2v1z"/>
                            </svg>
                        </button>
                        <button class="mermaid-modal-btn zoom-out" title="Zoom Out (Ctrl-)" data-action="zoom-out">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                                <path d="M7 9v1h5V9H7z"/>
                            </svg>
                        </button>
                        <button class="mermaid-modal-btn zoom-reset" title="Reset Zoom (Ctrl+0)" data-action="zoom-reset">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 5V1L7 6l5 5V7c3.31 0 6 2.69 6 6s-2.69 6-6 6-6-2.69-6-6H4c0 4.42 3.58 8 8 8s8-3.58 8-8-3.58-8-8-8z"/>
                            </svg>
                        </button>
                        <button class="mermaid-modal-btn fit-window" title="Fit to Window" data-action="fit-window">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"/>
                            </svg>
                        </button>
                        <div class="mermaid-zoom-level">100%</div>
                    </div>
                    <button class="mermaid-modal-btn close" title="Close (ESC)" data-action="close">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                        </svg>
                    </button>
                </div>
                <div class="mermaid-modal-body">
                    <div class="mermaid-modal-diagram"></div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        setupModalEventListeners(modal);
        return modal;
    }

    function setupModalEventListeners(modal) {
        const overlay = modal.querySelector('.mermaid-modal-overlay');
        const controls = modal.querySelector('.mermaid-modal-controls');
        const modalBody = modal.querySelector('.mermaid-modal-body');
        
        // Close modal on overlay click
        overlay.addEventListener('click', closeFullscreenModal);
        
        // Handle control buttons
        controls.addEventListener('click', (e) => {
            const action = e.target.closest('.mermaid-modal-btn')?.dataset.action;
            if (action) {
                e.preventDefault();
                e.stopPropagation();
                handleModalAction(action, modal);
            }
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (modal.style.display === 'flex') {
                handleModalKeyboard(e, modal);
            }
        });
        
        // Mouse wheel zoom in modal
        modalBody.addEventListener('wheel', (e) => {
            if (modal.style.display === 'flex') {
                e.preventDefault();
                const action = e.deltaY > 0 ? 'zoom-out' : 'zoom-in';
                handleModalAction(action, modal);
            }
        });
        
        // Drag functionality for modal
        let isDragging = false;
        let startX, startY;
        
        modalBody.addEventListener('mousedown', (e) => {
            if (e.button === 0) { // Left mouse button
                isDragging = true;
                startX = e.clientX;
                startY = e.clientY;
                modalBody.style.cursor = 'grabbing';
                e.preventDefault();
            }
        });
        
        document.addEventListener('mousemove', (e) => {
            if (isDragging && modal.style.display === 'flex') {
                const deltaX = e.clientX - startX;
                const deltaY = e.clientY - startY;
                
                const modalId = modal.dataset.currentDiagram;
                const state = zoomState.get(modalId);
                if (state) {
                    state.translateX += deltaX / 100;
                    state.translateY += deltaY / 100;
                    
                    const svg = modal.querySelector('svg');
                    if (svg) {
                        applyTransform(svg, state);
                        zoomState.set(modalId, state);
                    }
                }
                
                startX = e.clientX;
                startY = e.clientY;
            }
        });
        
        document.addEventListener('mouseup', () => {
            if (isDragging) {
                isDragging = false;
                modalBody.style.cursor = '';
            }
        });
        
        // Touch support for modal
        let lastTouchDistance = 0;
        modalBody.addEventListener('touchstart', (e) => {
            if (e.touches.length === 2) {
                const touch1 = e.touches[0];
                const touch2 = e.touches[1];
                lastTouchDistance = Math.sqrt(
                    Math.pow(touch2.clientX - touch1.clientX, 2) +
                    Math.pow(touch2.clientY - touch1.clientY, 2)
                );
            }
        });
        
        modalBody.addEventListener('touchmove', (e) => {
            if (e.touches.length === 2 && modal.style.display === 'flex') {
                e.preventDefault();
                const touch1 = e.touches[0];
                const touch2 = e.touches[1];
                const currentDistance = Math.sqrt(
                    Math.pow(touch2.clientX - touch1.clientX, 2) +
                    Math.pow(touch2.clientY - touch1.clientY, 2)
                );
                
                if (lastTouchDistance > 0) {
                    const scaleFactor = currentDistance / lastTouchDistance;
                    const modalId = modal.dataset.currentDiagram;
                    const svg = modal.querySelector('svg');
                    if (svg && modalId) {
                        zoomModalDiagram(svg, modalId, scaleFactor);
                    }
                }
                
                lastTouchDistance = currentDistance;
            }
        });
    }

    function handleModalAction(action, modal) {
        const modalId = modal.dataset.currentDiagram;
        const svg = modal.querySelector('svg');
        
        if (!svg || !modalId) return;
        
        switch (action) {
            case 'zoom-in':
                zoomModalDiagram(svg, modalId, 1.2);
                break;
            case 'zoom-out':
                zoomModalDiagram(svg, modalId, 0.8);
                break;
            case 'zoom-reset':
                resetModalZoom(svg, modalId);
                break;
            case 'fit-window':
                fitToWindow(svg, modalId);
                break;
            case 'close':
                closeFullscreenModal();
                break;
        }
        
        updateZoomLevel(modal, modalId);
    }

    function handleModalKeyboard(e, modal) {
        if (e.key === 'Escape') {
            e.preventDefault();
            closeFullscreenModal();
        } else if (e.ctrlKey || e.metaKey) {
            switch (e.key) {
                case '=':
                case '+':
                    e.preventDefault();
                    handleModalAction('zoom-in', modal);
                    break;
                case '-':
                    e.preventDefault();
                    handleModalAction('zoom-out', modal);
                    break;
                case '0':
                    e.preventDefault();
                    handleModalAction('zoom-reset', modal);
                    break;
            }
        }
    }

    function zoomModalDiagram(svg, modalId, scaleFactor) {
        const state = zoomState.get(modalId);
        if (!state) return;
        
        const newScale = Math.max(0.1, Math.min(10, state.scale * scaleFactor));
        state.scale = newScale;
        applyTransform(svg, state);
        zoomState.set(modalId, state);
    }

    function resetModalZoom(svg, modalId) {
        const state = zoomState.get(modalId);
        if (!state) return;
        
        state.scale = 1;
        state.translateX = 0;
        state.translateY = 0;
        applyTransform(svg, state);
        zoomState.set(modalId, state);
    }

    function fitToWindow(svg, modalId) {
        const modal = document.getElementById('mermaid-fullscreen-modal');
        const modalBody = modal.querySelector('.mermaid-modal-body');
        const state = zoomState.get(modalId);
        
        if (!state || !modalBody) return;
        
        // Get dimensions
        const svgRect = svg.getBoundingClientRect();
        const modalRect = modalBody.getBoundingClientRect();
        
        // Calculate scale to fit
        const padding = 40;
        const scaleX = (modalRect.width - padding) / svgRect.width;
        const scaleY = (modalRect.height - padding) / svgRect.height;
        const optimalScale = Math.min(scaleX, scaleY, 1);
        
        // Apply fit
        state.scale = optimalScale;
        state.translateX = 0;
        state.translateY = 0;
        applyTransform(svg, state);
        zoomState.set(modalId, state);
    }

    function updateZoomLevel(modal, modalId) {
        const zoomLevelEl = modal.querySelector('.mermaid-zoom-level');
        const state = zoomState.get(modalId);
        
        if (zoomLevelEl && state) {
            zoomLevelEl.textContent = Math.round(state.scale * 100) + '%';
        }
    }

    function closeFullscreenModal() {
        const modal = document.getElementById('mermaid-fullscreen-modal');
        if (modal) {
            modal.style.display = 'none';
            document.body.style.overflow = '';
        }
    }

    // Open fullscreen modal
    function openFullscreenModal(svg, diagramId, originalContainer) {
        if (currentModal) {
            closeModal();
        }
        
        // Create modal
        const modal = document.createElement('div');
        modal.className = 'mermaid-modal';
        modal.innerHTML = `
            <div class="mermaid-modal-backdrop"></div>
            <div class="mermaid-modal-content">
                <div class="mermaid-modal-header">
                    <div class="mermaid-modal-title">Mermaid Diagram</div>
                    <div class="mermaid-modal-controls">
                        <button class="mermaid-modal-btn" data-action="zoom-in" title="Zoom In (Ctrl++)">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                                <path d="M12 10h-2v2H9v-2H7V9h2V7h1v2h2v1z"/>
                            </svg>
                        </button>
                        <button class="mermaid-modal-btn" data-action="zoom-out" title="Zoom Out (Ctrl+-)">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                                <path d="M7 9v1h5V9H7z"/>
                            </svg>
                        </button>
                        <button class="mermaid-modal-btn" data-action="zoom-reset" title="Reset Zoom (Ctrl+0)">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 5V1L7 6l5 5V7c3.31 0 6 2.69 6 6s-2.69 6-6 6-6-2.69-6-6H4c0 4.42 3.58 8 8 8s8-3.58 8-8-3.58-8-8-8z"/>
                            </svg>
                        </button>
                        <button class="mermaid-modal-btn" data-action="zoom-fit" title="Fit to Window">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                                <path d="M3 3v6h6V7H5V3H3zm12 0v2h4v4h2V3h-6zm4 16v-4h-2v6h6v-2h-4zM9 21v-2H3v-6H1v8h8z"/>
                            </svg>
                        </button>
                        <button class="mermaid-modal-btn close-btn" data-action="close" title="Close (ESC)">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="mermaid-modal-body">
                    <div class="mermaid-modal-diagram-container"></div>
                </div>
            </div>
        `;
        
        // Clone the SVG for modal
        const svgClone = svg.cloneNode(true);
        const modalDiagramContainer = modal.querySelector('.mermaid-modal-diagram-container');
        modalDiagramContainer.appendChild(svgClone);
        
        // Initialize modal zoom state
        const modalDiagramId = `${diagramId}-modal`;
        zoomState.set(modalDiagramId, {
            scale: 1,
            translateX: 0,
            translateY: 0,
            originalViewBox: svgClone.getAttribute('viewBox') || '0 0 100 100'
        });
        
        // Add modal to DOM
        document.body.appendChild(modal);
        currentModal = modal;
        
        // Add event listeners
        setupModalEventListeners(modal, svgClone, modalDiagramId);
        
        // Prevent body scroll
        document.body.style.overflow = 'hidden';
        
        // Focus management
        modal.setAttribute('tabindex', '-1');
        modal.focus();
        
        // Fit diagram to modal initially
        setTimeout(() => {
            fitDiagramToContainer(svgClone, modalDiagramId, modalDiagramContainer);
        }, 100);
    }

    // Setup modal event listeners
    function setupModalEventListeners(modal, svg, modalDiagramId) {
        const backdrop = modal.querySelector('.mermaid-modal-backdrop');
        const controls = modal.querySelector('.mermaid-modal-controls');
        const diagramContainer = modal.querySelector('.mermaid-modal-diagram-container');
        
        // Control buttons
        controls.addEventListener('click', (e) => {
            const action = e.target.closest('.mermaid-modal-btn')?.dataset.action;
            if (action) {
                e.preventDefault();
                e.stopPropagation();
                handleModalAction(action, svg, modalDiagramId, diagramContainer);
            }
        });
        
        // Close on backdrop click
        backdrop.addEventListener('click', closeModal);
        
        // Close on escape key
        document.addEventListener('keydown', handleModalKeydown);
        
        // Modal drag functionality
        addModalDragFunctionality(diagramContainer, svg, modalDiagramId);
        
        // Modal wheel zoom
        diagramContainer.addEventListener('wheel', (e) => {
            e.preventDefault();
            const delta = e.deltaY > 0 ? 0.9 : 1.1;
            zoomModalDiagram(svg, modalDiagramId, delta, e.clientX, e.clientY, diagramContainer);
        });
    }

    // Handle modal actions
    function handleModalAction(action, svg, modalDiagramId, container) {
        switch (action) {
            case 'zoom-in':
                zoomModalDiagram(svg, modalDiagramId, 1.2, null, null, container);
                break;
            case 'zoom-out':
                zoomModalDiagram(svg, modalDiagramId, 0.8, null, null, container);
                break;
            case 'zoom-reset':
                resetModalZoom(svg, modalDiagramId);
                break;
            case 'zoom-fit':
                fitDiagramToContainer(svg, modalDiagramId, container);
                break;
            case 'close':
                closeModal();
                break;
        }
    }

    // Modal keyboard handler
    function handleModalKeydown(e) {
        if (!currentModal) return;
        
        const svg = currentModal.querySelector('.mermaid-modal-diagram-container svg');
        const modalDiagramId = Array.from(zoomState.keys()).find(id => id.includes('-modal'));
        const container = currentModal.querySelector('.mermaid-modal-diagram-container');
        
        switch (e.key) {
            case 'Escape':
                e.preventDefault();
                closeModal();
                break;
            case '=':
            case '+':
                if (e.ctrlKey || e.metaKey) {
                    e.preventDefault();
                    zoomModalDiagram(svg, modalDiagramId, 1.2, null, null, container);
                }
                break;
            case '-':
                if (e.ctrlKey || e.metaKey) {
                    e.preventDefault();
                    zoomModalDiagram(svg, modalDiagramId, 0.8, null, null, container);
                }
                break;
            case '0':
                if (e.ctrlKey || e.metaKey) {
                    e.preventDefault();
                    resetModalZoom(svg, modalDiagramId);
                }
                break;
        }
    }

    // Add modal drag functionality
    function addModalDragFunctionality(container, svg, modalDiagramId) {
        let isDragging = false;
        let startX, startY;
        let startTranslateX, startTranslateY;
        
        container.addEventListener('mousedown', (e) => {
            if (e.button === 0) { // Left mouse button
                isDragging = true;
                startX = e.clientX;
                startY = e.clientY;
                
                const state = zoomState.get(modalDiagramId);
                startTranslateX = state.translateX;
                startTranslateY = state.translateY;
                
                container.style.cursor = 'grabbing';
                e.preventDefault();
            }
        });
        
        document.addEventListener('mousemove', (e) => {
            if (!isDragging) return;
            
            const deltaX = (e.clientX - startX) / container.offsetWidth;
            const deltaY = (e.clientY - startY) / container.offsetHeight;
            
            const state = zoomState.get(modalDiagramId);
            state.translateX = startTranslateX + deltaX / state.scale;
            state.translateY = startTranslateY + deltaY / state.scale;
            
            applyTransform(svg, state);
            zoomState.set(modalDiagramId, state);
        });
        
        document.addEventListener('mouseup', () => {
            if (isDragging) {
                isDragging = false;
                container.style.cursor = 'grab';
            }
        });
        
        // Set initial cursor
        container.style.cursor = 'grab';
    }

    // Zoom modal diagram
    function zoomModalDiagram(svg, modalDiagramId, scaleFactor, centerX, centerY, container) {
        const state = zoomState.get(modalDiagramId);
        if (!state) return;
        
        const newScale = Math.max(0.1, Math.min(10, state.scale * scaleFactor));
        
        if (centerX && centerY) {
            const rect = container.getBoundingClientRect();
            const x = (centerX - rect.left) / rect.width;
            const y = (centerY - rect.top) / rect.height;
            
            state.translateX = x - (x - state.translateX) * (newScale / state.scale);
            state.translateY = y - (y - state.translateY) * (newScale / state.scale);
        }
        
        state.scale = newScale;
        applyTransform(svg, state);
        zoomState.set(modalDiagramId, state);
    }

    // Reset modal zoom
    function resetModalZoom(svg, modalDiagramId) {
        const state = zoomState.get(modalDiagramId);
        if (!state) return;
        
        state.scale = 1;
        state.translateX = 0;
        state.translateY = 0;
        applyTransform(svg, state);
        zoomState.set(modalDiagramId, state);
    }

    // Fit diagram to container
    function fitDiagramToContainer(svg, modalDiagramId, container) {
        const state = zoomState.get(modalDiagramId);
        if (!state) return;
        
        // Reset transform to get natural size
        svg.style.transform = '';
        
        const svgRect = svg.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();
        
        const scaleX = (containerRect.width - 40) / svgRect.width;
        const scaleY = (containerRect.height - 40) / svgRect.height;
        const scale = Math.min(scaleX, scaleY, 1); // Don't scale up beyond 100%
        
        state.scale = scale;
        state.translateX = 0;
        state.translateY = 0;
        
        applyTransform(svg, state);
        zoomState.set(modalDiagramId, state);
    }

    // Close modal
    function closeModal() {
        if (currentModal) {
            // Remove modal-specific zoom state
            const modalKeys = Array.from(zoomState.keys()).filter(key => key.includes('-modal'));
            modalKeys.forEach(key => zoomState.delete(key));
            
            // Remove event listeners
            document.removeEventListener('keydown', handleModalKeydown);
            
            // Remove modal
            currentModal.remove();
            currentModal = null;
            
            // Restore body scroll
            document.body.style.overflow = '';
        }
    }

    // Function to load Mermaid library dynamically
    function loadMermaid() {
        return new Promise((resolve, reject) => {
            if (typeof mermaid !== 'undefined') {
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js';
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    // Initialize when DOM is ready
    function init() {
        // Check if we're on a post page or if there are mermaid blocks
        const hasMermaidContent = document.querySelector('pre code.language-mermaid, pre code.mermaid, .mermaid');
        
        if (!hasMermaidContent) {
            return;
        }

        loadMermaid()
            .then(initMermaid)
            .catch(error => {
                console.error('Failed to load Mermaid:', error);
            });
    }

    // Start initialization
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // Support for theme switching
    function handleThemeChange() {
        if (typeof mermaid !== 'undefined') {
            // Detect current theme
            const isDark = document.documentElement.classList.contains('dark') || 
                          document.body.classList.contains('dark') ||
                          window.matchMedia('(prefers-color-scheme: dark)').matches;
            
            const newTheme = isDark ? 'dark' : 'default';
            if (defaultConfig.theme !== newTheme) {
                defaultConfig.theme = newTheme;
                mermaid.initialize(defaultConfig);
                
                // Re-render existing diagrams
                const diagrams = document.querySelectorAll('.mermaid-diagram');
                diagrams.forEach(diagram => {
                    diagram.removeAttribute('data-processed');
                    diagram.innerHTML = diagram.getAttribute('data-original-content') || diagram.innerHTML;
                });
                
                try {
                    mermaid.init(undefined, '.mermaid-diagram');
                } catch (error) {
                    console.error('Mermaid theme switch error:', error);
                }
            }
        }
    }
    
    // Listen for theme changes
    window.addEventListener('theme-changed', handleThemeChange);
    
    // Listen for system theme changes
    if (window.matchMedia) {
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', handleThemeChange);
    }

})();
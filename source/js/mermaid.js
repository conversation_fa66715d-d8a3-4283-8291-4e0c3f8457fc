/**
 * Mermaid.js integration for Claudia theme
 * Automatically detects and renders Mermaid diagrams in posts
 */

(function() {
    'use strict';

    // Check if Mermaid is enabled in theme config
    if (!window.CLAUDIA_CONFIG || !window.CLAUDIA_CONFIG.mermaid || !window.CLAUDIA_CONFIG.mermaid.enable) {
        return;
    }

    // Default Mermaid configuration
    const defaultConfig = {
        theme: window.CLAUDIA_CONFIG.mermaid.theme || 'default',
        startOnLoad: false,
        themeVariables: {
            primaryColor: '#409eff',
            primaryTextColor: '#fff',
            primaryBorderColor: '#409eff',
            lineColor: '#409eff',
            secondaryColor: '#ecf5ff',
            tertiaryColor: '#f5f7fa'
        },
        flowchart: {
            useMaxWidth: true,
            htmlLabels: true,
            curve: 'basis'
        },
        sequence: {
            useMaxWidth: true,
            diagramMarginX: 50,
            diagramMarginY: 10,
            actorMargin: 50,
            width: 150,
            height: 65,
            boxMargin: 10,
            boxTextMargin: 5,
            noteMargin: 10,
            messageMargin: 35,
            mirrorActors: true,
            bottomMarginAdj: 1,
            useMaxWidth: true,
            rightAngles: false,
            showSequenceNumbers: false
        },
        gantt: {
            useMaxWidth: true,
            leftPadding: 75,
            rightPadding: 20,
            gridLineStartPadding: 230,
            fontSize: 11,
            fontFamily: '"Open Sans", sans-serif',
            numberSectionStyles: 4
        }
    };

    // Function to initialize Mermaid
    function initMermaid() {
        if (typeof mermaid === 'undefined') {
            console.warn('Mermaid library not loaded');
            return;
        }

        // Initialize Mermaid with configuration
        mermaid.initialize(defaultConfig);

        // Find all Mermaid code blocks
        const mermaidBlocks = document.querySelectorAll('pre code.language-mermaid, pre code.mermaid, .mermaid');
        
        if (mermaidBlocks.length === 0) {
            return;
        }

        // Process each Mermaid block
        mermaidBlocks.forEach((block, index) => {
            const isCodeBlock = block.tagName.toLowerCase() === 'code';
            const content = isCodeBlock ? block.textContent : block.innerHTML;
            
            // Create container for the diagram
            const diagramId = `mermaid-diagram-${index}`;
            const container = document.createElement('div');
            container.className = 'mermaid-container';
            
            const diagramDiv = document.createElement('div');
            diagramDiv.id = diagramId;
            diagramDiv.className = 'mermaid-diagram';
            diagramDiv.innerHTML = content;
            // Store original content for theme switching
            diagramDiv.setAttribute('data-original-content', content);
            
            container.appendChild(diagramDiv);
            
            // Replace the original block
            if (isCodeBlock) {
                const preElement = block.closest('pre');
                if (preElement) {
                    preElement.parentNode.replaceChild(container, preElement);
                }
            } else {
                block.parentNode.replaceChild(container, block);
            }
        });

        // Render all diagrams
        try {
            mermaid.init(undefined, '.mermaid-diagram');
        } catch (error) {
            console.error('Mermaid rendering error:', error);
        }
    }

    // Function to load Mermaid library dynamically
    function loadMermaid() {
        return new Promise((resolve, reject) => {
            if (typeof mermaid !== 'undefined') {
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js';
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    // Initialize when DOM is ready
    function init() {
        // Check if we're on a post page or if there are mermaid blocks
        const hasMermaidContent = document.querySelector('pre code.language-mermaid, pre code.mermaid, .mermaid');
        
        if (!hasMermaidContent) {
            return;
        }

        loadMermaid()
            .then(initMermaid)
            .catch(error => {
                console.error('Failed to load Mermaid:', error);
            });
    }

    // Start initialization
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // Support for theme switching
    function handleThemeChange() {
        if (typeof mermaid !== 'undefined') {
            // Detect current theme
            const isDark = document.documentElement.classList.contains('dark') || 
                          document.body.classList.contains('dark') ||
                          window.matchMedia('(prefers-color-scheme: dark)').matches;
            
            const newTheme = isDark ? 'dark' : 'default';
            if (defaultConfig.theme !== newTheme) {
                defaultConfig.theme = newTheme;
                mermaid.initialize(defaultConfig);
                
                // Re-render existing diagrams
                const diagrams = document.querySelectorAll('.mermaid-diagram');
                diagrams.forEach(diagram => {
                    diagram.removeAttribute('data-processed');
                    diagram.innerHTML = diagram.getAttribute('data-original-content') || diagram.innerHTML;
                });
                
                try {
                    mermaid.init(undefined, '.mermaid-diagram');
                } catch (error) {
                    console.error('Mermaid theme switch error:', error);
                }
            }
        }
    }
    
    // Listen for theme changes
    window.addEventListener('theme-changed', handleThemeChange);
    
    // Listen for system theme changes
    if (window.matchMedia) {
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', handleThemeChange);
    }

})();
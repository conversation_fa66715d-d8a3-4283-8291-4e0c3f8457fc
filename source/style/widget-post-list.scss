@use "common/variable" as *;
$avatarSize: 180px;
$coverMaxHeight: 360px;
$coverBlurEffect: 20px;

.post-container {
  .post-item-card {
    margin-bottom: 15px;

    overflow: hidden;
    box-shadow: $boxShadow;
    border-radius: $borderRadius;
    background: var(--second-bg-color);
    border: 1px solid var(--border-line-color-2);

    // cover image
    > header {
      overflow: hidden;

      // fix composite layer border on safari browser
      z-index: 1;
      border-top-left-radius: $borderRadius;
      border-top-right-radius: $borderRadius;

      .post-cover-backdrop {
        position: absolute;
        z-index: 1;
        width: 100%;

        img {
          width: 100%;
          height: $coverMaxHeight;

          object-fit: cover;
          transform: scale(1.2);
          filter: blur($coverBlurEffect) ;
        }
      }

      .post-cover-link {
        position: relative;
        z-index: 2;

        width: 100%;

        // fix white spacer
        font-size: 0;
      }

      .post-cover-img {
        max-height: $coverMaxHeight;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);

        will-change: transform;
        transition: transform 500ms;
      }

      &:hover .post-cover-img {
        transform: scale(1.04);
      }
    }

    .post-card-content {
      word-break: break-word;

      time {
        font-size: 14px;
      }
    }
  }

  .aside-card-container {
    padding: 10px;
    margin-bottom: 15px;
    box-shadow: $boxShadow;
    border-radius: $borderRadius;
    background: var(--second-bg-color);
    border: 1px solid var(--border-line-color-2);
  }

  .categories-widget,
  .archives-widget,
  .recent-widget,
  .friend-widget,
  .tag-widget, {
    h3 {
      font-weight: bold;
      margin-bottom: 10px;
    }

    .category-list .category-list-item,
    .archive-list .archive-list-item {
      display: flex;
      align-items: center;
      justify-content: space-between;

      margin-bottom: 5px;
      color: var(--second-text-color);

      .category-list-count,
      .archive-list-count {
        padding: 0 12px;
        border-radius: 11px;
        background: var(--third-bg-color);
      }
    }
  }

  .profile-widget {
    min-height: 300px;
    padding-bottom: 0;

    img {
      object-fit: cover;
    }

    .avatar{
      position: relative;

      width: $avatarSize;
      height: $avatarSize;
      margin-top: 10px;

      // fix on safari will-change bugs
      z-index: 1;

      border-radius: 50%;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.08);
      border: 7px solid var(--avatar-border-color);
    }

    .avatar-placeholder:after {
      content: "\e62a";
      font-size: 80px;
      font-family: iconfont;

      position: absolute;
      top: 50%;
      left: 50%;
      z-index: 1;
      transform: translate(-50%, -50%);
    }

    .user-name {
      font-size: 18px;
      font-weight: bold;

      margin: 15px 0 2px;
    }

    // user describe quote style
    blockquote:before {
      content: '“';
      margin-right: 5px;
      vertical-align: middle;
    }

    blockquote:after {
      content: '”';
      margin-left: 5px;
      vertical-align: bottom;
    }

    blockquote:before,
    blockquote:after {
      line-height: 0;
      font-size: 32px;
      color: #585858;
      font-family: sans-serif;
    }

    address {
      margin-top: 14px;
      font-style: normal;
    }

    .sns-container {
      overflow: auto;
      padding: 5px 0;
      margin-top: 15px;
      border-top: 1px solid var(--border-line-color);

      i {
        margin: 0 8px;
        font-size: 30px;
      }
    }
  }
  .recent-widget {
    ul li {
      margin-bottom: 15px;

      &:last-child {
        section {
          border-bottom: none;
        }
      }
    }
    img, .post-img-placeholder {
      width: 50px;
      height: 50px;
      margin-right: 5px;
      object-fit: cover;
      border-radius: $borderRadius;
    }

    section {
      border-bottom: 1px solid var(--border-line-color);

      h4 {
        font-size: 14px;
        line-height: 20px;
        font-weight: bold;
      }

      time {
        font-size: 12px;
      }
    }
  }

  .archives-widget {
      > section {
        overflow: auto;
        max-height: 45vh;
      }

      &.is-in-archive-page > section {
        max-height: 80vh;
      }
  }

  .tag-widget {
    > section {
      overflow: auto;
      max-height: 30vh;
    }

    &.is-in-tag-page > section {
      max-height: 80vh;
    }
  }

  .category-page {
    > section {
      overflow: auto;
      max-height: 40vh;
    }

    &.categories-widget > section {
      max-height: 80vh;
    }
  }
  // turn page
  .paginator {
    .page-number,
    .extend {
      padding: 2px 15px;
      margin: 0 5px 10px;

      border-radius: $borderRadius;
      color: var(--second-text-color);
      background: var(--third-bg-color);

      &.current {
        color: #fff!important;
        background: $activeColor;
      }
    }
  }
}

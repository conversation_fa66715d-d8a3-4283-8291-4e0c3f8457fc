@use "common/variable" as *;

$lineColor: var(--border-line-color);

#postTopic {
  cursor: pointer;
  will-change: transform;
  transform: translateY(100%);

  p {
    color: var(--primary-text-color);
  }

  &.is-show-scrollToTop-tips {
    transition: transform 300ms linear 300ms;
    transform: translateY(-100%);
    //background: red;
  }

  &.is-flash-scrollToTop-tips {
    transition: transform 300ms linear 900ms;
    transform: translateY(0);
  }

  &.is-switch-post-title {
    transition: none;
    transform: translateY(0);
    //background: blue;
  }

  &.is-show-post-title {
    transition: transform 300ms linear;
    transform: translateY(0);
    //background: darkorchid;
  }

  &.is-hidden-topic-bar {
    transition: transform 100ms linear;
    transform: translateY(100%);
    //background: #00c4a7;
  }

  &.immediately-show {
    transition: none;
    transform: translateY(0);
    //background: #545454;
  }
}

.post-page {
  .post-content {

    code {
      border-radius: 4px;
      background: var(--third-bg-color);
    }

    pre {
      padding: 0;
      background: transparent;

      code {
        padding: 15px;
        border: 1px solid $lineColor;
        background: var(--pre-code-bg-color);
      }
    }

    .hljs {
      color: var(--second-text-color);
    }

    img {
      display: block;
      margin: 0 auto;
      max-height: 500px;

      border-radius: $borderRadius;
      box-shadow: 0 0 15px rgba(0, 0, 0, .05);

      opacity: 0;
      will-change: opacity;
    }

    a {
      color: $activeColor;
    }
  }

  .jump-container .button{
    max-width: calc(50% - 5px);
    span {
      overflow: hidden;
      white-space: nowrap;
      text-overflow:ellipsis;
    }
  }

  .comment-container {
    border-top: 1px solid $lineColor;
  }

  .toc {
    position: sticky;
    top: 60px;

    margin-left: 0;
    margin-right: 0;
    padding-left: 15px;
    height: calc(100vh - 100px);

    overflow: auto;
    list-style: none!important;
    border-left: 1px solid $lineColor;

    &::-webkit-scrollbar {
      display: none;
    }

    ol {
      margin-top: 5px;
      margin-left: 15px;
      list-style: none!important;
    }

    .is-active {
      span {
        color: $activeColor!important;
      }
    }
  }

  :target {
    padding-top: 60px;
    margin-top: -60px!important;
  }
}

@use "common/variable" as *;

/**
 * Enhanced Mermaid diagram styles for Claudia theme
 * Features: Responsive design, inline zoom controls, fullscreen modal
 */

// Mermaid container styles
.mermaid-container {
  position: relative;
  margin: 2rem 0;
  padding: 1rem;
  background: var(--post-bg-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  
  @media (max-width: 768px) {
    margin: 1rem 0;
    padding: 0.5rem;
    border-radius: 4px;
  }
  
  &:hover {
    .mermaid-controls {
      opacity: 1;
      visibility: visible;
    }
  }
  
  &.enhanced {
    cursor: grab;
    
    &:active {
      cursor: grabbing;
    }
  }
}

// Mermaid diagram wrapper
.mermaid-diagram {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100px;
  transition: transform 0.2s ease;
  
  // Ensure diagrams are responsive
  svg {
    max-width: 100% !important;
    height: auto !important;
    transition: transform 0.2s ease;
    
    // Fix for mobile devices
    @media (max-width: 768px) {
      font-size: 12px;
    }
  }
}

// Inline control overlay
.mermaid-controls {
  position: absolute;
  top: 8px;
  right: 8px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 10;
  
  @media (max-width: 768px) {
    opacity: 1;
    visibility: visible;
  }
}

.mermaid-inline-controls {
  display: flex;
  gap: 4px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 6px;
  padding: 4px;
  backdrop-filter: blur(8px);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
}

.mermaid-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  background: transparent;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
  }
  
  &:active {
    transform: scale(0.95);
  }
  
  svg {
    width: 16px;
    height: 16px;
    fill: currentColor;
  }
}

// Loading state
.mermaid-diagram:not([data-processed]) {
  &::before {
    content: "Loading diagram...";
    display: block;
    text-align: center;
    color: var(--text-color-light);
    font-style: italic;
    padding: 2rem;
  }
}

// Error state
.mermaid-diagram.error {
  &::before {
    content: "Error rendering diagram";
    color: var(--error-color, #f56565);
  }
}

// Theme-specific overrides for better integration
.mermaid {
  // Flowchart styles
  .node rect,
  .node circle,
  .node ellipse,
  .node polygon {
    fill: var(--mermaid-node-bg, #f9f9f9);
    stroke: var(--mermaid-node-border, #409eff);
    stroke-width: 2px;
  }
  
  .node .label {
    color: var(--mermaid-text-color, #333);
    font-family: var(--font-family);
  }
  
  // Edge styles
  .edgePath .path {
    stroke: var(--mermaid-edge-color, #409eff);
    stroke-width: 2px;
  }
  
  .edgeLabel {
    background-color: var(--mermaid-label-bg, #fff);
    color: var(--mermaid-text-color, #333);
    font-family: var(--font-family);
    font-size: 12px;
  }
  
  // Sequence diagram styles
  .actor {
    fill: var(--mermaid-actor-bg, #eee);
    stroke: var(--mermaid-actor-border, #409eff);
  }
  
  .actor-line {
    stroke: var(--mermaid-line-color, #999);
  }
  
  .messageLine0,
  .messageLine1 {
    stroke: var(--mermaid-message-color, #409eff);
  }
  
  .messageText {
    fill: var(--mermaid-text-color, #333);
    font-family: var(--font-family);
  }
  
  // Gantt chart styles
  .section0,
  .section1,
  .section2,
  .section3 {
    fill: var(--mermaid-section-bg, #f9f9f9);
  }
  
  .task0,
  .task1,
  .task2,
  .task3 {
    fill: var(--mermaid-task-bg, #409eff);
  }
  
  .taskText0,
  .taskText1,
  .taskText2,
  .taskText3 {
    fill: var(--mermaid-text-color, #333);
    font-family: var(--font-family);
  }
  
  // Git graph styles
  .commit-id,
  .commit-msg,
  .branch-label {
    fill: var(--mermaid-text-color, #333);
    font-family: var(--font-family);
  }
}

// Dark theme adjustments
[data-theme="dark"] {
  --mermaid-node-bg: #2d3748;
  --mermaid-node-border: #63b3ed;
  --mermaid-text-color: #e2e8f0;
  --mermaid-edge-color: #63b3ed;
  --mermaid-label-bg: #1a202c;
  --mermaid-actor-bg: #2d3748;
  --mermaid-actor-border: #63b3ed;
  --mermaid-line-color: #718096;
  --mermaid-message-color: #63b3ed;
  --mermaid-section-bg: #2d3748;
  --mermaid-task-bg: #63b3ed;
  
  .mermaid-container {
    background: var(--post-bg-color-dark, #1a202c);
    border-color: var(--border-color-dark, #2d3748);
  }
}

// High contrast mode adjustments
@media (prefers-contrast: high) {
  .mermaid {
    .node rect,
    .node circle,
    .node ellipse,
    .node polygon {
      stroke-width: 3px;
    }
    
    .edgePath .path {
      stroke-width: 3px;
    }
  }
}

// Print styles
@media print {
  .mermaid-container {
    background: white !important;
    border: 1px solid #000 !important;
    break-inside: avoid;
  }
  
  .mermaid-diagram svg {
    max-width: 100% !important;
    page-break-inside: avoid;
  }
}

// Animation for diagram loading
.mermaid-diagram[data-processed] {
  animation: fadeInDiagram 0.3s ease-in-out;
}

@keyframes fadeInDiagram {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Fullscreen modal styles
.mermaid-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeInModal 0.3s ease;
}

.mermaid-modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
}

.mermaid-modal-content {
  position: relative;
  width: 90vw;
  height: 90vh;
  max-width: 1200px;
  max-height: 800px;
  background: var(--post-bg-color);
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  
  @media (max-width: 768px) {
    width: 95vw;
    height: 95vh;
    border-radius: 8px;
  }
}

.mermaid-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: var(--header-bg-color, #f8f9fa);
  border-bottom: 1px solid var(--border-color);
  
  @media (max-width: 768px) {
    padding: 12px 16px;
  }
}

.mermaid-modal-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
  
  @media (max-width: 768px) {
    font-size: 16px;
  }
}

.mermaid-modal-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.mermaid-modal-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 6px;
  background: transparent;
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: var(--hover-bg-color, rgba(0, 0, 0, 0.1));
    transform: scale(1.05);
  }
  
  &:active {
    transform: scale(0.95);
  }
  
  &.close-btn {
    color: #ef4444;
    
    &:hover {
      background: rgba(239, 68, 68, 0.1);
    }
  }
  
  svg {
    width: 20px;
    height: 20px;
    fill: currentColor;
  }
  
  @media (max-width: 768px) {
    width: 36px;
    height: 36px;
    
    svg {
      width: 18px;
      height: 18px;
    }
  }
}

.mermaid-modal-body {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  overflow: hidden;
  
  @media (max-width: 768px) {
    padding: 16px;
  }
}

.mermaid-modal-diagram-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  cursor: grab;
  
  &:active {
    cursor: grabbing;
  }
  
  svg {
    max-width: none !important;
    max-height: none !important;
    transition: transform 0.2s ease;
  }
}

// Modal animations
@keyframes fadeInModal {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// Enhanced dark mode adjustments
[data-theme="dark"] {
  .mermaid-modal-header {
    background: var(--header-bg-color-dark, #2d3748);
    border-bottom-color: var(--border-color-dark, #4a5568);
  }
  
  .mermaid-modal-content {
    background: var(--post-bg-color-dark, #1a202c);
  }
  
  .mermaid-modal-btn:hover {
    background: var(--hover-bg-color-dark, rgba(255, 255, 255, 0.1));
  }
}

// High contrast mode for controls
@media (prefers-contrast: high) {
  .mermaid-btn, .mermaid-modal-btn {
    border: 1px solid currentColor;
  }
  
  .mermaid-inline-controls {
    border: 1px solid rgba(255, 255, 255, 0.5);
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .mermaid-container,
  .mermaid-diagram svg,
  .mermaid-btn,
  .mermaid-modal-btn,
  .mermaid-modal,
  .mermaid-modal-diagram-container svg {
    transition: none;
  }
  
  .mermaid-modal {
    animation: none;
  }
}

// Enhanced Interactive Controls
.mermaid-controls {
  position: absolute;
  top: 8px;
  right: 8px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  z-index: 10;
  
  @media (max-width: 768px) {
    opacity: 1;
    visibility: visible;
  }
}

.mermaid-inline-controls {
  display: flex;
  gap: 4px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  border-radius: 6px;
  padding: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.1);
  
  [data-theme="dark"] & {
    background: rgba(26, 32, 44, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
  }
}

.mermaid-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: none;
  background: transparent;
  border-radius: 4px;
  cursor: pointer;
  color: var(--text-color);
  transition: all 0.2s ease;
  
  &:hover {
    background: rgba(64, 158, 255, 0.1);
    color: #409eff;
  }
  
  &:active {
    transform: scale(0.95);
  }
  
  svg {
    width: 16px;
    height: 16px;
  }
}

// Fullscreen Modal Styles
.mermaid-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  display: none;
  align-items: center;
  justify-content: center;
  animation: modalFadeIn 0.3s ease-out;
}

.mermaid-modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
}

.mermaid-modal-content {
  position: relative;
  width: 90vw;
  height: 90vh;
  max-width: 1200px;
  max-height: 800px;
  background: var(--post-bg-color, #fff);
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  
  [data-theme="dark"] & {
    background: var(--post-bg-color-dark, #1a202c);
  }
  
  @media (max-width: 768px) {
    width: 95vw;
    height: 95vh;
    border-radius: 8px;
  }
}

.mermaid-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: rgba(0, 0, 0, 0.05);
  border-bottom: 1px solid var(--border-color);
  
  [data-theme="dark"] & {
    background: rgba(255, 255, 255, 0.05);
    border-bottom-color: var(--border-color-dark);
  }
}

.mermaid-modal-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mermaid-modal-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  color: var(--text-color);
  transition: all 0.2s ease;
  
  &:hover {
    background: rgba(64, 158, 255, 0.1);
    color: #409eff;
  }
  
  &:active {
    transform: scale(0.95);
  }
  
  &.close {
    color: #f56565;
    
    &:hover {
      background: rgba(245, 101, 101, 0.1);
    }
  }
  
  svg {
    width: 20px;
    height: 20px;
  }
}

.mermaid-zoom-level {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-color);
  min-width: 50px;
  text-align: center;
  padding: 0 8px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  
  [data-theme="dark"] & {
    background: rgba(255, 255, 255, 0.05);
  }
}

.mermaid-modal-body {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  cursor: grab;
  
  &:active {
    cursor: grabbing;
  }
}

.mermaid-modal-diagram {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  
  svg {
    max-width: 100%;
    max-height: 100%;
    transition: transform 0.2s ease;
  }
}

// Modal animations
@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// Keyboard shortcut hints (optional enhancement)
.mermaid-modal-shortcuts {
  position: absolute;
  bottom: 16px;
  left: 16px;
  font-size: 12px;
  color: var(--text-color-light);
  opacity: 0.7;
  
  @media (max-width: 768px) {
    display: none;
  }
}

// Touch-friendly enhancements
@media (hover: none) and (pointer: coarse) {
  .mermaid-btn,
  .mermaid-modal-btn {
    width: 44px;
    height: 44px;
  }
  
  .mermaid-inline-controls {
    padding: 6px;
  }
  
  .mermaid-modal-controls {
    gap: 12px;
  }
}

// Accessibility improvements
.mermaid-btn:focus,
.mermaid-modal-btn:focus {
  outline: 2px solid #409eff;
  outline-offset: 2px;
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .mermaid-modal,
  .mermaid-controls,
  .mermaid-btn,
  .mermaid-modal-btn,
  .mermaid-modal-diagram svg {
    transition: none;
    animation: none;
  }
  
  .mermaid-diagram[data-processed] {
    animation: none;
  }
}
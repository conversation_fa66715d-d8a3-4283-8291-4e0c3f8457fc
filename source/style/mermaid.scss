@use "common/variable" as *;

/**
 * Mermaid diagram styles for Claudia theme
 * Provides responsive and theme-integrated styling for Mermaid diagrams
 */

// Mermaid container styles
.mermaid-container {
  margin: 2rem 0;
  padding: 1rem;
  background: var(--post-bg-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow-x: auto;
  position: relative;
  
  @media (max-width: 768px) {
    margin: 1rem 0;
    padding: 0.5rem;
    border-radius: 4px;
  }
}

// Mermaid diagram wrapper
.mermaid-diagram {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100px;
  
  // Ensure diagrams display at natural size and center fallback
  svg {
    max-width: none !important;
    width: auto !important;
    height: auto !important;
    display: block;
    margin: 0 auto; // center when not wrapped
    
    // Fix for mobile devices: allow readable font size
    @media (max-width: 768px) {
      font-size: 12px;
    }
  }
}

// Inline zoom viewport/content wrappers
.mermaid-inline-viewport {
  overflow: auto;
  width: 100%;
  display: flex;
  justify-content: center; // center inline content horizontally
}

.mermaid-inline-content {
  display: inline-block;
  transform: scale(1);
  transform-origin: 0 0;
}

// Do not shrink inline SVGs; use intrinsic width and allow horizontal scroll
.mermaid-inline-content svg {
  max-width: none !important;
  width: auto !important;
}

// Toolbar for inline controls
.mermaid-toolbar {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 6px;
  background: color-mix(in srgb, var(--primary-bg-color) 85%, transparent);
  border: 1px solid var(--border-color);
  box-shadow: var(--boxShadow);
  border-radius: 6px;
  padding: 4px;
  z-index: 1;
}

.mermaid-btn {
  appearance: none;
  border: 1px solid var(--border-color);
  background: var(--third-bg-color);
  color: var(--second-text-color);
  border-radius: 4px;
  width: 28px;
  height: 28px;
  line-height: 26px;
  text-align: center;
  padding: 0;
  cursor: pointer;
  font-weight: 600;
}

.mermaid-btn:hover {
  background: var(--primary-bg-color);
}

.mermaid-btn.close {
  width: auto;
  padding: 0 8px;
}

// Modal styles
.mermaid-modal {
  position: fixed;
  inset: 0;
  display: none;
  z-index: 1000;
}

.mermaid-modal.open {
  display: block;
}

.mermaid-modal-backdrop {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
}

.mermaid-modal-dialog {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: min(95vw, 1200px);
  height: min(90vh, 800px);
  background: var(--primary-bg-color);
  color: var(--second-text-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  box-shadow: var(--boxShadow);
  display: flex;
  flex-direction: column;
}

.mermaid-modal-toolbar {
  display: flex;
  gap: 8px;
  padding: 8px;
  border-bottom: 1px solid var(--border-color);
  justify-content: flex-end;
}

.mermaid-modal-viewport {
  position: relative;
  flex: 1;
  overflow: auto;
  padding: 12px;
}

.mermaid-modal-content {
  display: inline-block;
  transform: scale(1);
  transform-origin: 0 0;
}

// Loading state
.mermaid-diagram:not([data-processed]) {
  &::before {
    content: "Loading diagram...";
    display: block;
    text-align: center;
    color: var(--text-color-light);
    font-style: italic;
    padding: 2rem;
  }
}

// Error state
.mermaid-diagram.error {
  &::before {
    content: "Error rendering diagram";
    color: var(--error-color, #f56565);
  }
}

// Theme-specific overrides for better integration
.mermaid {
  // Flowchart styles
  .node rect,
  .node circle,
  .node ellipse,
  .node polygon {
    fill: var(--mermaid-node-bg, #f9f9f9);
    stroke: var(--mermaid-node-border, #409eff);
    stroke-width: 2px;
  }
  
  .node .label {
    color: var(--mermaid-text-color, #333);
    font-family: var(--font-family);
  }
  
  // Edge styles
  .edgePath .path {
    stroke: var(--mermaid-edge-color, #409eff);
    stroke-width: 2px;
  }
  
  .edgeLabel {
    background-color: var(--mermaid-label-bg, #fff);
    color: var(--mermaid-text-color, #333);
    font-family: var(--font-family);
    font-size: 12px;
  }
  
  // Sequence diagram styles
  .actor {
    fill: var(--mermaid-actor-bg, #eee);
    stroke: var(--mermaid-actor-border, #409eff);
  }
  
  .actor-line {
    stroke: var(--mermaid-line-color, #999);
  }
  
  .messageLine0,
  .messageLine1 {
    stroke: var(--mermaid-message-color, #409eff);
  }
  
  .messageText {
    fill: var(--mermaid-text-color, #333);
    font-family: var(--font-family);
  }
  
  // Gantt chart styles
  .section0,
  .section1,
  .section2,
  .section3 {
    fill: var(--mermaid-section-bg, #f9f9f9);
  }
  
  .task0,
  .task1,
  .task2,
  .task3 {
    fill: var(--mermaid-task-bg, #409eff);
  }
  
  .taskText0,
  .taskText1,
  .taskText2,
  .taskText3 {
    fill: var(--mermaid-text-color, #333);
    font-family: var(--font-family);
  }
  
  // Git graph styles
  .commit-id,
  .commit-msg,
  .branch-label {
    fill: var(--mermaid-text-color, #333);
    font-family: var(--font-family);
  }
}

// Dark theme adjustments
[data-theme="dark"] {
  --mermaid-node-bg: #2d3748;
  --mermaid-node-border: #63b3ed;
  --mermaid-text-color: #e2e8f0;
  --mermaid-edge-color: #63b3ed;
  --mermaid-label-bg: #1a202c;
  --mermaid-actor-bg: #2d3748;
  --mermaid-actor-border: #63b3ed;
  --mermaid-line-color: #718096;
  --mermaid-message-color: #63b3ed;
  --mermaid-section-bg: #2d3748;
  --mermaid-task-bg: #63b3ed;
  
  .mermaid-container {
    background: var(--post-bg-color-dark, #1a202c);
    border-color: var(--border-color-dark, #2d3748);
  }
}

// High contrast mode adjustments
@media (prefers-contrast: high) {
  .mermaid {
    .node rect,
    .node circle,
    .node ellipse,
    .node polygon {
      stroke-width: 3px;
    }
    
    .edgePath .path {
      stroke-width: 3px;
    }
  }
}

// Print styles
@media print {
  .mermaid-container {
    background: white !important;
    border: 1px solid #000 !important;
    break-inside: avoid;
  }
  
  .mermaid-diagram svg {
    max-width: 100% !important;
    page-break-inside: avoid;
  }
}

// Animation for diagram loading
.mermaid-diagram[data-processed] {
  animation: fadeInDiagram 0.3s ease-in-out;
}

@keyframes fadeInDiagram {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
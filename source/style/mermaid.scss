@use "common/variable" as *;

/**
 * Mermaid diagram styles for Claudia theme
 * Provides responsive and theme-integrated styling for Mermaid diagrams
 */

// Mermaid container styles
.mermaid-container {
  position: relative;
  margin: 2rem 0;
  background: var(--post-bg-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
  
  @media (max-width: 768px) {
    margin: 1rem 0;
    border-radius: 4px;
  }
}

// Mermaid toolbar
.mermaid-toolbar {
  display: flex;
  justify-content: flex-end;
  padding: 8px 12px;
  background: var(--toolbar-bg, #f8f9fa);
  border-bottom: 1px solid var(--border-color);
  
  @media (max-width: 768px) {
    padding: 6px 8px;
  }
}

.mermaid-zoom-controls {
  display: flex;
  gap: 4px;
}

// Mermaid buttons
.mermaid-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
  padding: 4px 8px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background: var(--button-bg, #ffffff);
  color: var(--button-text, #333333);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: var(--button-hover-bg, #e9ecef);
    border-color: var(--button-hover-border, #007bff);
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  @media (max-width: 768px) {
    min-width: 28px;
    height: 28px;
    font-size: 11px;
  }
}

// Diagram wrapper
.mermaid-diagram-wrapper {
  position: relative;
  padding: 1rem;
  overflow: visible;
  
  @media (max-width: 768px) {
    padding: 0.5rem;
  }
}

// Mermaid diagram wrapper
.mermaid-diagram {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100px;
  
  // Ensure diagrams are responsive
  svg {
    max-width: 100% !important;
    height: auto !important;
    
    // Fix for mobile devices
    @media (max-width: 768px) {
      font-size: 12px;
    }
  }
}

// Loading state
.mermaid-diagram:not([data-processed]) {
  &::before {
    content: "Loading diagram...";
    display: block;
    text-align: center;
    color: var(--text-color-light);
    font-style: italic;
    padding: 2rem;
  }
}

// Error state
.mermaid-diagram.error {
  &::before {
    content: "Error rendering diagram";
    color: var(--error-color, #f56565);
  }
}

// Theme-specific overrides for better integration
.mermaid {
  // Flowchart styles
  .node rect,
  .node circle,
  .node ellipse,
  .node polygon {
    fill: var(--mermaid-node-bg, #f9f9f9);
    stroke: var(--mermaid-node-border, #409eff);
    stroke-width: 2px;
  }
  
  .node .label {
    color: var(--mermaid-text-color, #333);
    font-family: var(--font-family);
  }
  
  // Edge styles
  .edgePath .path {
    stroke: var(--mermaid-edge-color, #409eff);
    stroke-width: 2px;
  }
  
  .edgeLabel {
    background-color: var(--mermaid-label-bg, #fff);
    color: var(--mermaid-text-color, #333);
    font-family: var(--font-family);
    font-size: 12px;
  }
  
  // Sequence diagram styles
  .actor {
    fill: var(--mermaid-actor-bg, #eee);
    stroke: var(--mermaid-actor-border, #409eff);
  }
  
  .actor-line {
    stroke: var(--mermaid-line-color, #999);
  }
  
  .messageLine0,
  .messageLine1 {
    stroke: var(--mermaid-message-color, #409eff);
  }
  
  .messageText {
    fill: var(--mermaid-text-color, #333);
    font-family: var(--font-family);
  }
  
  // Gantt chart styles
  .section0,
  .section1,
  .section2,
  .section3 {
    fill: var(--mermaid-section-bg, #f9f9f9);
  }
  
  .task0,
  .task1,
  .task2,
  .task3 {
    fill: var(--mermaid-task-bg, #409eff);
  }
  
  .taskText0,
  .taskText1,
  .taskText2,
  .taskText3 {
    fill: var(--mermaid-text-color, #333);
    font-family: var(--font-family);
  }
  
  // Git graph styles
  .commit-id,
  .commit-msg,
  .branch-label {
    fill: var(--mermaid-text-color, #333);
    font-family: var(--font-family);
  }
}

// Dark theme adjustments
[data-theme="dark"] {
  --mermaid-node-bg: #2d3748;
  --mermaid-node-border: #63b3ed;
  --mermaid-text-color: #e2e8f0;
  --mermaid-edge-color: #63b3ed;
  --mermaid-label-bg: #1a202c;
  --mermaid-actor-bg: #2d3748;
  --mermaid-actor-border: #63b3ed;
  --mermaid-line-color: #718096;
  --mermaid-message-color: #63b3ed;
  --mermaid-section-bg: #2d3748;
  --mermaid-task-bg: #63b3ed;
  
  .mermaid-container {
    background: var(--post-bg-color-dark, #1a202c);
    border-color: var(--border-color-dark, #2d3748);
  }
}

// High contrast mode adjustments
@media (prefers-contrast: high) {
  .mermaid {
    .node rect,
    .node circle,
    .node ellipse,
    .node polygon {
      stroke-width: 3px;
    }
    
    .edgePath .path {
      stroke-width: 3px;
    }
  }
}

// Print styles
@media print {
  .mermaid-container {
    background: white !important;
    border: 1px solid #000 !important;
    break-inside: avoid;
  }
  
  .mermaid-diagram svg {
    max-width: 100% !important;
    page-break-inside: avoid;
  }
}

// Animation for diagram loading
.mermaid-diagram[data-processed] {
  animation: fadeInDiagram 0.3s ease-in-out;
}

@keyframes fadeInDiagram {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// ===== MODAL STYLES =====

.mermaid-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  
  &.active {
    opacity: 1;
    visibility: visible;
  }
}

.mermaid-modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
}

.mermaid-modal-content {
  position: relative;
  width: 95%;
  height: 95%;
  max-width: 1400px;
  max-height: 900px;
  background: var(--modal-bg, #ffffff);
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  
  @media (max-width: 768px) {
    width: 98%;
    height: 98%;
    border-radius: 8px;
  }
}

.mermaid-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--modal-header-bg, #f8f9fa);
  border-bottom: 1px solid var(--border-color);
  flex-shrink: 0;
}

.mermaid-modal-controls {
  display: flex;
  gap: 6px;
  
  .mermaid-btn {
    min-width: 36px;
    height: 36px;
    font-size: 14px;
    
    @media (max-width: 768px) {
      min-width: 32px;
      height: 32px;
      font-size: 12px;
    }
  }
}

.modal-close {
  background: var(--close-btn-bg, #dc3545) !important;
  color: white !important;
  border-color: var(--close-btn-bg, #dc3545) !important;
  
  &:hover {
    background: var(--close-btn-hover-bg, #c82333) !important;
    border-color: var(--close-btn-hover-bg, #c82333) !important;
  }
}

.mermaid-modal-body {
  flex: 1;
  overflow: auto;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  @media (max-width: 768px) {
    padding: 10px;
  }
}

.mermaid-modal-diagram-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: auto;
}

.modal-diagram {
  transition: transform 0.2s ease;
  
  svg {
    max-width: none !important;
    max-height: none !important;
  }
}

// Dark theme adjustments for modal
[data-theme="dark"] {
  --modal-bg: #1a202c;
  --modal-header-bg: #2d3748;
  --toolbar-bg: #2d3748;
  --button-bg: #4a5568;
  --button-text: #e2e8f0;
  --button-hover-bg: #2d3748;
  --button-hover-border: #63b3ed;
  --close-btn-bg: #e53e3e;
  --close-btn-hover-bg: #c53030;
}

// High contrast mode for buttons
@media (prefers-contrast: high) {
  .mermaid-btn {
    border-width: 2px;
    font-weight: bold;
  }
}

// Animation for modal appearance
.mermaid-modal-content {
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.mermaid-modal.active .mermaid-modal-content {
  transform: scale(1);
}

// Responsive adjustments
@media (max-width: 480px) {
  .mermaid-modal-controls {
    gap: 3px;
    
    .mermaid-btn {
      min-width: 28px;
      height: 28px;
      font-size: 10px;
      padding: 2px 4px;
    }
  }
  
  .mermaid-toolbar {
    padding: 4px 6px;
  }
  
  .mermaid-zoom-controls {
    gap: 2px;
  }
}
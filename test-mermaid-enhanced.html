<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mermaid 增强功能测试</title>
    <link rel="stylesheet" href="source/style/mermaid.scss">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: #f8f9fa;
        }
        
        .test-section {
            margin: 40px 0;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1, h2 {
            color: #24292e;
        }
        
        h2 {
            border-bottom: 2px solid #e1e4e8;
            padding-bottom: 10px;
        }
        
        .feature-list {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        
        .feature-list ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .feature-list li {
            margin: 8px 0;
        }
        
        .instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .mermaid-container {
            --post-bg-color: #ffffff;
            --border-color: #e1e4e8;
            --toolbar-bg: #f8f9fa;
            --button-bg: #ffffff;
            --button-text: #333333;
            --button-hover-bg: #e9ecef;
            --button-hover-border: #007bff;
        }
        
        .dark-theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            z-index: 1000;
        }
        
        body.dark {
            background: #1a202c;
            color: #e2e8f0;
        }
        
        body.dark .test-section {
            background: #2d3748;
            color: #e2e8f0;
        }
        
        body.dark .mermaid-container {
            --post-bg-color: #2d3748;
            --border-color: #4a5568;
            --toolbar-bg: #1a202c;
            --button-bg: #4a5568;
            --button-text: #e2e8f0;
            --button-hover-bg: #2d3748;
            --button-hover-border: #63b3ed;
        }
    </style>
</head>
<body>
    <button class="dark-theme-toggle" onclick="toggleTheme()">🌓 切换主题</button>
    
    <h1>🚀 Mermaid 增强功能测试</h1>
    
    <div class="instructions">
        <strong>📋 测试说明：</strong>
        <ul>
            <li>每个图表右上角都有缩放控件工具栏</li>
            <li>点击 🔍+ 和 🔍- 进行缩放，⚡ 重置缩放</li>
            <li>点击 ⛶ 按钮打开全屏模态框</li>
            <li>在模态框中可以使用更多缩放控件，包括 📐 适应窗口</li>
            <li>按 ESC 键或点击外部区域关闭模态框</li>
            <li>支持 Ctrl+滚轮 进行缩放</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>测试 1: 复杂流程图</h2>
        <div class="feature-list">
            <strong>测试功能：</strong>
            <ul>
                <li>内联缩放控件</li>
                <li>全屏模态框</li>
                <li>鼠标滚轮缩放</li>
            </ul>
        </div>
        
        <pre><code class="language-mermaid">
graph TB
    subgraph "用户层"
        A[Web浏览器]
        B[移动应用]
        C[桌面应用]
    end
    
    subgraph "网关层"
        D[API网关]
        E[负载均衡器]
        F[认证服务]
    end
    
    subgraph "服务层"
        G[用户服务]
        H[订单服务]
        I[支付服务]
        J[库存服务]
        K[通知服务]
    end
    
    subgraph "数据层"
        L[(用户数据库)]
        M[(订单数据库)]
        N[(支付数据库)]
        O[(库存数据库)]
        P[Redis缓存]
        Q[消息队列]
    end
    
    A --> D
    B --> D
    C --> D
    D --> E
    E --> F
    F --> G
    F --> H
    F --> I
    F --> J
    F --> K
    
    G --> L
    H --> M
    I --> N
    J --> O
    
    G --> P
    H --> P
    I --> Q
    J --> Q
    K --> Q
        </code></pre>
    </div>
    
    <div class="test-section">
        <h2>测试 2: 详细序列图</h2>
        <div class="feature-list">
            <strong>测试功能：</strong>
            <ul>
                <li>模态框中的适应窗口功能</li>
                <li>复杂图表的可读性改善</li>
                <li>响应式缩放</li>
            </ul>
        </div>
        
        <pre><code class="language-mermaid">
sequenceDiagram
    participant U as 用户
    participant F as 前端应用
    participant G as API网关
    participant A as 认证服务
    participant O as 订单服务
    participant P as 支付服务
    participant I as 库存服务
    participant N as 通知服务
    participant D as 数据库
    participant Q as 消息队列
    
    U->>F: 1. 提交订单请求
    F->>G: 2. 转发订单请求
    G->>A: 3. 验证用户身份
    A-->>G: 4. 返回验证结果
    
    alt 身份验证成功
        G->>O: 5. 创建订单
        O->>I: 6. 检查库存
        I-->>O: 7. 返回库存状态
        
        alt 库存充足
            O->>D: 8. 保存订单信息
            D-->>O: 9. 确认保存
            O->>P: 10. 发起支付请求
            P->>D: 11. 记录支付信息
            P-->>O: 12. 返回支付结果
            
            alt 支付成功
                O->>I: 13. 扣减库存
                I->>D: 14. 更新库存
                O->>Q: 15. 发送订单完成消息
                Q->>N: 16. 触发通知
                N->>U: 17. 发送确认通知
                O-->>G: 18. 返回成功结果
            else 支付失败
                O->>Q: 19. 发送支付失败消息
                O-->>G: 20. 返回支付失败
            end
        else 库存不足
            O-->>G: 21. 返回库存不足
        end
    else 身份验证失败
        G-->>F: 22. 返回认证失败
    end
    
    G-->>F: 23. 返回最终结果
    F-->>U: 24. 显示处理结果
        </code></pre>
    </div>
    
    <div class="test-section">
        <h2>测试 3: 复杂类图</h2>
        <div class="feature-list">
            <strong>测试功能：</strong>
            <ul>
                <li>大图表的全屏查看</li>
                <li>精确缩放控制</li>
                <li>主题切换适配</li>
            </ul>
        </div>
        
        <pre><code class="language-mermaid">
classDiagram
    class User {
        +Long id
        +String username
        +String email
        +String password
        +Date createdAt
        +Date updatedAt
        +login()
        +logout()
        +updateProfile()
        +changePassword()
    }
    
    class Order {
        +Long id
        +Long userId
        +String orderNumber
        +OrderStatus status
        +BigDecimal totalAmount
        +Date orderDate
        +Date deliveryDate
        +String shippingAddress
        +calculateTotal()
        +updateStatus()
        +cancel()
        +confirm()
    }
    
    class OrderItem {
        +Long id
        +Long orderId
        +Long productId
        +Integer quantity
        +BigDecimal unitPrice
        +BigDecimal totalPrice
        +calculateSubtotal()
        +updateQuantity()
    }
    
    class Product {
        +Long id
        +String name
        +String description
        +String category
        +BigDecimal price
        +Integer stockQuantity
        +String imageUrl
        +Date createdAt
        +updateStock()
        +updatePrice()
        +isAvailable()
    }
    
    class Payment {
        +Long id
        +Long orderId
        +PaymentMethod method
        +PaymentStatus status
        +BigDecimal amount
        +Date paymentDate
        +String transactionId
        +processPayment()
        +refund()
        +verify()
    }
    
    class Inventory {
        +Long id
        +Long productId
        +Integer quantity
        +Integer reservedQuantity
        +Date lastUpdated
        +reserve()
        +release()
        +updateQuantity()
        +checkAvailability()
    }
    
    class Notification {
        +Long id
        +Long userId
        +String title
        +String content
        +NotificationType type
        +Boolean isRead
        +Date createdAt
        +send()
        +markAsRead()
    }
    
    User ||--o{ Order : places
    Order ||--o{ OrderItem : contains
    OrderItem }o--|| Product : references
    Order ||--o| Payment : has
    Product ||--o| Inventory : tracked_by
    User ||--o{ Notification : receives
    Order ||--o{ Notification : generates
    
    <<enumeration>> OrderStatus
    OrderStatus : PENDING
    OrderStatus : CONFIRMED
    OrderStatus : SHIPPED
    OrderStatus : DELIVERED
    OrderStatus : CANCELLED
    
    <<enumeration>> PaymentMethod
    PaymentMethod : CREDIT_CARD
    PaymentMethod : DEBIT_CARD
    PaymentMethod : PAYPAL
    PaymentMethod : BANK_TRANSFER
    
    <<enumeration>> PaymentStatus
    PaymentStatus : PENDING
    PaymentStatus : COMPLETED
    PaymentStatus : FAILED
    PaymentStatus : REFUNDED
    
    <<enumeration>> NotificationType
    NotificationType : ORDER_CONFIRMATION
    NotificationType : PAYMENT_SUCCESS
    NotificationType : SHIPPING_UPDATE
    NotificationType : DELIVERY_CONFIRMATION
        </code></pre>
    </div>
    
    <div class="test-section">
        <h2>测试 4: 甘特图项目管理</h2>
        <div class="feature-list">
            <strong>测试功能：</strong>
            <ul>
                <li>时间轴图表的横向滚动</li>
                <li>模态框中的完整视图</li>
                <li>移动端适配</li>
            </ul>
        </div>
        
        <pre><code class="language-mermaid">
gantt
    title 电商平台开发项目时间线
    dateFormat  YYYY-MM-DD
    section 需求分析
    市场调研           :done,    des1, 2024-01-01, 2024-01-15
    用户需求收集        :done,    des2, 2024-01-10, 2024-01-25
    技术方案设计        :done,    des3, 2024-01-20, 2024-02-05
    架构设计           :done,    des4, 2024-01-25, 2024-02-10
    
    section 前端开发
    UI/UX设计         :done,    ui1, 2024-02-01, 2024-02-20
    组件库开发         :done,    ui2, 2024-02-15, 2024-03-05
    页面开发          :active,  ui3, 2024-02-25, 2024-03-20
    响应式适配         :         ui4, 2024-03-10, 2024-03-25
    
    section 后端开发
    数据库设计         :done,    be1, 2024-02-05, 2024-02-15
    API接口开发       :active,  be2, 2024-02-20, 2024-03-15
    业务逻辑实现       :         be3, 2024-03-01, 2024-03-25
    安全性实现         :         be4, 2024-03-10, 2024-03-30
    
    section 系统集成
    前后端联调         :         int1, 2024-03-20, 2024-04-05
    第三方服务集成     :         int2, 2024-03-25, 2024-04-10
    性能优化          :         int3, 2024-04-01, 2024-04-15
    
    section 测试阶段
    单元测试          :         test1, 2024-03-15, 2024-04-05
    集成测试          :         test2, 2024-04-01, 2024-04-15
    用户验收测试       :         test3, 2024-04-10, 2024-04-20
    性能测试          :         test4, 2024-04-15, 2024-04-25
    
    section 部署上线
    生产环境准备       :         deploy1, 2024-04-20, 2024-04-25
    灰度发布          :         deploy2, 2024-04-25, 2024-04-30
    正式上线          :         deploy3, 2024-04-30, 2024-05-05
    监控和维护         :         deploy4, 2024-05-01, 2024-05-15
        </code></pre>
    </div>

    <script>
        // 模拟主题配置
        window.CLAUDIA_CONFIG = {
            mermaid: {
                enable: true,
                theme: 'default'
            }
        };
        
        // 主题切换功能
        function toggleTheme() {
            document.body.classList.toggle('dark');
            const isDark = document.body.classList.contains('dark');
            
            // 触发主题变更事件
            window.dispatchEvent(new CustomEvent('theme-changed'));
            
            // 更新配置
            if (window.CLAUDIA_CONFIG) {
                window.CLAUDIA_CONFIG.mermaid.theme = isDark ? 'dark' : 'default';
            }
        }
        
        // 测试结果显示
        function showTestResults() {
            const containers = document.querySelectorAll('.mermaid-container');
            const toolbars = document.querySelectorAll('.mermaid-toolbar');
            const zoomControls = document.querySelectorAll('.mermaid-zoom-controls');
            
            console.log(`✅ 发现 ${containers.length} 个图表容器`);
            console.log(`✅ 发现 ${toolbars.length} 个工具栏`);
            console.log(`✅ 发现 ${zoomControls.length} 个缩放控件组`);
            
            // 测试按钮功能
            const firstContainer = containers[0];
            if (firstContainer) {
                const zoomInBtn = firstContainer.querySelector('.zoom-in');
                const fullscreenBtn = firstContainer.querySelector('.fullscreen');
                
                console.log(`✅ 缩放按钮: ${zoomInBtn ? '存在' : '缺失'}`);
                console.log(`✅ 全屏按钮: ${fullscreenBtn ? '存在' : '缺失'}`);
            }
        }
        
        // 页面加载完成后运行测试
        window.addEventListener('load', () => {
            setTimeout(showTestResults, 2000);
        });
    </script>
    
    <script src="source/js/mermaid.js"></script>
</body>
</html>
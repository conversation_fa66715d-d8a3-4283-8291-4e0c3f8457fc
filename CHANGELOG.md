# Changelog

## 11.06.2022
- Support Fancybox (thank you @jackfromeast)
- Add Friend Links
- Fix more detail Bugs
- Update Readme

## 04.12.2021
- Add Postcard image hover zoom animate effect
- Adjust page indicator top margin

## 04.08.2021
- Adjust layout details

## 03.26.2021
- Add Meta description
- Add Disqus comment
- Bug fix

## 03.24.2021
- Adjust header container max-width

## 03.22.2021
- Add Baidu Analytics
- Add Google Analytics

## 03.21.2021
- Support search
- User profile can be hidden

## 03.12.2021
- Add Valine comment system
- Adjust about page padding on mobile devices

## 03.08.2021
- Add footnotes

## 01.31.2021
- Fix if no have 0 length category、tag

## 01.20.2021
- Improve readability
- Fix i18N bugs
- Improve avatar UI

## 01.14.2021
- Add skeleton loading
- Fix maximize the height on the `archive` 、 `category` 、 `Tag` page
- Other details of optimization

## 01.12.2021
- Maximize the height on the `archive` 、 `category` 、 `Tag` page
- Hidden page indicator when only 1 page

## 01.11.2021
- Comments support dark mode
- Add click header title go back to the page top

## 01.09.2021
- Add topic bar
- Update 简体中文文档
- Add image load fadeIn animate

## 01.08.2021
- Add `zh-CN` language
- Hidden catalogue scroll bar
- Fix columns width maybe collapse bug
- Add in post page jump to next or prev post feature

## 01.05.2021
- Add dark mode
- Support `<--more-->` mark

## 01.03.2021
- Fix turn page indicator on the mobile device sometimes can't wrap
- Change page indicator active color
- Add RSS
- Add highlight current this chapter in Table of content
- Increase Table of content loading speed


## 01.01.2021
- Fix home page post content word wrap bug
- Support `utteranc` comments system
- Adjust post cover art
- Fix on Safari post image border radius not rending
- Fix more on the mobile device experience problem

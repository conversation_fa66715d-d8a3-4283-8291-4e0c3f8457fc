# Mermaid 图表支持指南

Claudia 主题现已支持 Mermaid 图表渲染，让您可以在 Markdown 文章中轻松创建各种类型的图表。

## 🚀 快速开始

### 1. 启用 Mermaid 支持

在主题配置文件 `_config.yml` 中启用 Mermaid：

```yaml
# Mermaid diagrams support
# Automatically renders Mermaid diagrams in posts
mermaid:
  enable: true
  # Available themes: default | dark | forest | neutral | base
  # The theme will automatically switch based on the current page theme
  theme: default
```

### 2. 在文章中使用 Mermaid

在 Markdown 文章中，使用 ````mermaid``` 代码块来创建图表：

````markdown
```mermaid
graph TD
    A[开始] --> B{判断条件}
    B -->|是| C[执行操作A]
    B -->|否| D[执行操作B]
    C --> E[结束]
    D --> E
```
````

## 📊 支持的图表类型

### 流程图 (Flowchart)

````markdown
```mermaid
graph TD
    A[矩形] --> B{菱形}
    B -->|是| C[圆角矩形]
    B -->|否| D[圆形]
```
````

### 序列图 (Sequence Diagram)

````markdown
```mermaid
sequenceDiagram
    participant A as 用户
    participant B as 系统
    A->>B: 发送请求
    B-->>A: 返回响应
    A->>B: 确认收到
```
````

### 甘特图 (Gantt Chart)

````markdown
```mermaid
gantt
    title 项目时间线
    dateFormat  YYYY-MM-DD
    section 阶段一
    任务1           :done,    des1, 2023-01-01,2023-01-06
    任务2           :active,  des2, 2023-01-07, 3d
    任务3           :         des3, after des2, 5d
```
````

### 类图 (Class Diagram)

````markdown
```mermaid
classDiagram
    Animal <|-- Duck
    Animal <|-- Fish
    Animal : +int age
    Animal : +String gender
    Animal: +isMammal()
    class Duck{
        +String beakColor
        +swim()
        +quack()
    }
```
````

### 状态图 (State Diagram)

````markdown
```mermaid
stateDiagram-v2
    [*] --> 空闲
    空闲 --> 工作中
    工作中 --> 空闲
    工作中 --> 错误
    错误 --> 空闲
    错误 --> [*]
```
````

### 用户旅程图 (User Journey)

````markdown
```mermaid
journey
    title 用户购买流程
    section 浏览商品
      浏览首页: 5: 用户
      搜索商品: 3: 用户
      查看详情: 4: 用户
    section 购买
      添加购物车: 2: 用户
      结账: 1: 用户
      支付: 2: 用户
```
````

### Git 图 (Git Graph)

````markdown
```mermaid
gitgraph
    commit
    branch develop
    checkout develop
    commit
    commit
    checkout main
    merge develop
    commit
```
````

## 🎨 主题配置

### 可用主题

- `default` - 默认浅色主题
- `dark` - 深色主题
- `forest` - 森林绿主题
- `neutral` - 中性主题
- `base` - 基础主题

### 自动主题切换

主题会根据页面的明暗模式自动调整 Mermaid 图表的颜色方案，确保在任何主题下都有良好的视觉效果。

## 📱 响应式设计

Mermaid 图表已经过优化，支持响应式设计：

- **桌面端**：完整显示所有图表细节
- **平板端**：自动调整字体大小和间距
- **移动端**：优化触摸体验，支持横向滚动

## 🛠️ 技术细节

### 自动检测

主题会自动检测以下格式的 Mermaid 代码块：

- ````mermaid``` 
- `<div class="mermaid">...</div>`
- `<pre><code class="language-mermaid">...</code></pre>`

### 延迟加载

为了优化性能，Mermaid.js 库只在页面包含 Mermaid 图表时才会加载。

### 错误处理

如果图表语法有误，会显示友好的错误提示，不会影响页面的其他内容。

## 🎯 最佳实践

### 1. 保持语法简洁

```mermaid
graph LR
    A --> B --> C
```

比复杂的嵌套结构更容易阅读和维护。

### 2. 使用有意义的标签

```mermaid
graph TD
    用户登录 --> 验证身份
    验证身份 --> 访问系统
```

使用中文或有描述性的英文标签，提高图表可读性。

### 3. 适当使用颜色和样式

```mermaid
graph TD
    A[正常流程] --> B[处理中]
    A --> C[异常流程]
    style C fill:#f96
```

### 4. 控制图表复杂度

对于复杂的系统，考虑拆分为多个简单的图表，而不是一个复杂的大图。

## 🐛 故障排除

### 图表不显示

1. 检查 `_config.yml` 中是否启用了 Mermaid
2. 确认代码块使用了正确的语法标记
3. 检查浏览器控制台是否有错误信息

### 图表样式异常

1. 清除浏览器缓存
2. 检查是否有 CSS 冲突
3. 尝试切换不同的 Mermaid 主题

### 移动端显示问题

1. 确保图表内容不要过于复杂
2. 使用简短的标签文本
3. 考虑为移动端创建简化版本的图表

## 📚 更多资源

- [Mermaid 官方文档](https://mermaid-js.github.io/mermaid/)
- [Mermaid 在线编辑器](https://mermaid.live/)
- [语法参考](https://mermaid-js.github.io/mermaid/#/README)

## 💡 示例文章

创建一个测试文章来验证 Mermaid 功能：

```markdown
---
title: Mermaid 图表测试
date: 2024-01-01
---

# 系统架构图

```mermaid
graph TB
    subgraph "前端"
        A[用户界面]
        B[路由管理]
        C[状态管理]
    end
    
    subgraph "后端"
        D[API网关]
        E[业务逻辑]
        F[数据访问层]
    end
    
    subgraph "数据库"
        G[(MySQL)]
        H[(Redis)]
    end
    
    A --> D
    B --> D
    C --> D
    D --> E
    E --> F
    F --> G
    F --> H
```

这个架构图展示了系统的主要组件和它们之间的关系。
```

保存这个文件并运行 Hexo 生成命令，您就能看到 Mermaid 图表的效果了！
# Mermaid 增强图表支持指南

Claudia 主题现已支持增强的 Mermaid 图表渲染，提供完整的交互功能，让您可以在 Markdown 文章中轻松创建和查看各种类型的图表。

## ✨ 增强功能特色

- 🔍 **内联缩放控制** - 直接在文章中缩放图表
- 🖼️ **全屏模态框** - 专用查看窗口，获得最佳视觉体验
- ⌨️ **键盘快捷键** - 快速操作，提高效率
- 📱 **移动端优化** - 触摸手势支持，响应式设计
- 🎨 **主题自适应** - 自动适配浅色/深色主题
- ♿ **无障碍支持** - 键盘导航和屏幕阅读器兼容

## 🚀 快速开始

### 1. 启用 Mermaid 支持

在主题配置文件 `_config.yml` 中启用 Mermaid：

```yaml
# Mermaid diagrams support
# Automatically renders Mermaid diagrams in posts
mermaid:
  enable: true
  # Available themes: default | dark | forest | neutral | base
  # The theme will automatically switch based on the current page theme
  theme: default
```

### 2. 在文章中使用 Mermaid

在 Markdown 文章中，使用 ````mermaid``` 代码块来创建图表：

````markdown
```mermaid
graph TD
    A[开始] --> B{判断条件}
    B -->|是| C[执行操作A]
    B -->|否| D[执行操作B]
    C --> E[结束]
    D --> E
```
````

### 3. 交互功能使用

每个渲染的图表都会自动获得增强的交互功能：

#### 内联控制
- **悬停显示**：鼠标悬停在图表上时，右上角会出现控制按钮
- **移动端**：在移动设备上，控制按钮始终可见

#### 全屏查看
- **点击全屏按钮**：打开专用的查看窗口
- **完整工具栏**：包含所有缩放和导航控件
- **实时反馈**：显示当前缩放百分比

#### 快捷操作
- **滚轮缩放**：在图表上使用鼠标滚轮进行缩放
- **拖拽平移**：在全屏模式下拖拽图表进行平移
- **键盘控制**：使用快捷键快速操作

## 🎮 交互控制详解

### 内联控制按钮

每个图表右上角的控制按钮：

| 按钮 | 功能 | 说明 |
|------|------|------|
| 🔍+ | 放大 | 增加图表缩放级别 |
| 🔍- | 缩小 | 减少图表缩放级别 |
| 🔄 | 重置 | 恢复默认缩放大小 |
| 🖼️ | 全屏 | 在专用窗口中打开图表 |

### 键盘快捷键

在全屏模式下可使用以下快捷键：

| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `ESC` | 关闭 | 关闭全屏模态框 |
| `Ctrl + +` | 放大 | 增加缩放级别 |
| `Ctrl + -` | 缩小 | 减少缩放级别 |
| `Ctrl + 0` | 重置 | 恢复默认缩放 |

### 鼠标和触摸操作

| 操作 | 功能 | 适用场景 |
|------|------|----------|
| 鼠标滚轮 | 缩放 | 内联和全屏模式 |
| 拖拽 | 平移 | 仅全屏模式 |
| 双指捏合 | 缩放 | 移动端触摸屏 |
| 单击背景 | 关闭 | 全屏模式 |

### 全屏模态框功能

全屏查看窗口提供：
- **完整工具栏**：包含所有缩放控制按钮
- **缩放指示器**：实时显示当前缩放百分比
- **适应窗口**：一键调整图表以适应窗口大小
- **拖拽平移**：支持鼠标拖拽移动图表位置
- **键盘导航**：完整的键盘快捷键支持

## 📊 支持的图表类型

### 流程图 (Flowchart)

````markdown
```mermaid
graph TD
    A[矩形] --> B{菱形}
    B -->|是| C[圆角矩形]
    B -->|否| D[圆形]
```
````

### 序列图 (Sequence Diagram)

````markdown
```mermaid
sequenceDiagram
    participant A as 用户
    participant B as 系统
    A->>B: 发送请求
    B-->>A: 返回响应
    A->>B: 确认收到
```
````

### 甘特图 (Gantt Chart)

````markdown
```mermaid
gantt
    title 项目时间线
    dateFormat  YYYY-MM-DD
    section 阶段一
    任务1           :done,    des1, 2023-01-01,2023-01-06
    任务2           :active,  des2, 2023-01-07, 3d
    任务3           :         des3, after des2, 5d
```
````

### 类图 (Class Diagram)

````markdown
```mermaid
classDiagram
    Animal <|-- Duck
    Animal <|-- Fish
    Animal : +int age
    Animal : +String gender
    Animal: +isMammal()
    class Duck{
        +String beakColor
        +swim()
        +quack()
    }
```
````

### 状态图 (State Diagram)

````markdown
```mermaid
stateDiagram-v2
    [*] --> 空闲
    空闲 --> 工作中
    工作中 --> 空闲
    工作中 --> 错误
    错误 --> 空闲
    错误 --> [*]
```
````

### 用户旅程图 (User Journey)

````markdown
```mermaid
journey
    title 用户购买流程
    section 浏览商品
      浏览首页: 5: 用户
      搜索商品: 3: 用户
      查看详情: 4: 用户
    section 购买
      添加购物车: 2: 用户
      结账: 1: 用户
      支付: 2: 用户
```
````

### Git 图 (Git Graph)

````markdown
```mermaid
gitgraph
    commit
    branch develop
    checkout develop
    commit
    commit
    checkout main
    merge develop
    commit
```
````

## 🎨 主题配置

### 可用主题

- `default` - 默认浅色主题
- `dark` - 深色主题
- `forest` - 森林绿主题
- `neutral` - 中性主题
- `base` - 基础主题

### 自动主题切换

主题会根据页面的明暗模式自动调整 Mermaid 图表的颜色方案，确保在任何主题下都有良好的视觉效果。

## 📱 响应式设计

Mermaid 图表已经过优化，支持响应式设计：

- **桌面端**：完整显示所有图表细节
- **平板端**：自动调整字体大小和间距
- **移动端**：优化触摸体验，支持横向滚动

## 🛠️ 技术细节

### 自动检测

主题会自动检测以下格式的 Mermaid 代码块：

- ````mermaid``` 
- `<div class="mermaid">...</div>`
- `<pre><code class="language-mermaid">...</code></pre>`

### 延迟加载

为了优化性能，Mermaid.js 库只在页面包含 Mermaid 图表时才会加载。

### 错误处理

如果图表语法有误，会显示友好的错误提示，不会影响页面的其他内容。

## 🎯 最佳实践

### 1. 保持语法简洁

```mermaid
graph LR
    A --> B --> C
```

比复杂的嵌套结构更容易阅读和维护。

### 2. 使用有意义的标签

```mermaid
graph TD
    用户登录 --> 验证身份
    验证身份 --> 访问系统
```

使用中文或有描述性的英文标签，提高图表可读性。

### 3. 适当使用颜色和样式

```mermaid
graph TD
    A[正常流程] --> B[处理中]
    A --> C[异常流程]
    style C fill:#f96
```

### 4. 控制图表复杂度

对于复杂的系统，考虑拆分为多个简单的图表，而不是一个复杂的大图。

## 🐛 故障排除

### 图表不显示

1. 检查 `_config.yml` 中是否启用了 Mermaid
2. 确认代码块使用了正确的语法标记
3. 检查浏览器控制台是否有错误信息

### 图表样式异常

1. 清除浏览器缓存
2. 检查是否有 CSS 冲突
3. 尝试切换不同的 Mermaid 主题

### 移动端显示问题

1. 确保图表内容不要过于复杂
2. 使用简短的标签文本
3. 考虑为移动端创建简化版本的图表

## 📚 更多资源

- [Mermaid 官方文档](https://mermaid-js.github.io/mermaid/)
- [Mermaid 在线编辑器](https://mermaid.live/)
- [语法参考](https://mermaid-js.github.io/mermaid/#/README)

## 💡 示例文章

创建一个测试文章来验证 Mermaid 功能：

```markdown
---
title: Mermaid 图表测试
date: 2024-01-01
---

# 系统架构图

```mermaid
graph TB
    subgraph "前端"
        A[用户界面]
        B[路由管理]
        C[状态管理]
    end
    
    subgraph "后端"
        D[API网关]
        E[业务逻辑]
        F[数据访问层]
    end
    
    subgraph "数据库"
        G[(MySQL)]
        H[(Redis)]
    end
    
    A --> D
    B --> D
    C --> D
    D --> E
    E --> F
    F --> G
    F --> H
```

这个架构图展示了系统的主要组件和它们之间的关系。
```

保存这个文件并运行 Hexo 生成命令，您就能看到 Mermaid 图表的效果了！